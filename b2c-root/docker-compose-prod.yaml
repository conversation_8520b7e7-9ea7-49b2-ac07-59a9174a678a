name: root
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.9.1
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    networks:
      - backend-network
    healthcheck:
      test: ["CMD-SHELL", "nc -z localhost 2181 || exit 1"]
      interval: 10s
      timeout: 10s
      retries: 15
      start_period: 10s
    restart: on-failure

  kafka-backend:  
    image: confluentinc/cp-kafka:7.9.1
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-backend:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 3000
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    ports:
      - "9092:9092"
    networks:
      - backend-network
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics --bootstrap-server kafka-backend:9092 --list || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 15
      start_period: 10s
    restart: on-failure

  kafka-socket:
    image: confluentinc/cp-kafka:7.9.1
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-socket:9093
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 3000
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    ports:
      - "9093:9093"
    networks:
      - backend-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "kafka-socket:9093", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5

  backend-service:
    build: 
      context: ../b2c-backend-service
      dockerfile: Dockerfile.prod
    env_file:
      - .env
    ports:
      - '${BACKEND_PORT}:${BACKEND_PORT}'
    networks:
      - backend-network
    depends_on:
      kafka-backend:
        condition: service_healthy
    restart: on-failure

  communication-service:
    build:
      context: ../b2c-communication-service
      dockerfile: ./Dockerfile.prod
    env_file:
      - .env
    ports:
      - '${COMMUNICATION_PORT}:${COMMUNICATION_PORT}'
    networks:
      - backend-network
    depends_on:
      kafka-backend:
        condition: service_healthy
    restart: on-failure

  chat-service:
    build:
      context: ../b2c-chat-service
      dockerfile: ./Dockerfile.prod
    env_file:
      - .env
    ports:
      - '${CHAT_PORT}:${CHAT_PORT}'
    networks:
      - backend-network
    depends_on:
      kafka-socket:
        condition: service_healthy

  socket-service:
    build:
      context: ../b2c-socket-service
      dockerfile: ./Dockerfile.prod
    env_file:
      - .env
    ports:
      - '${SOCKET_PORT}:${SOCKET_PORT}'
    networks:
      - backend-network
    depends_on:
      kafka-socket:
        condition: service_healthy

  crons-service:
    build:
      context: ../b2c-crons-service
      dockerfile: ./Dockerfile.prod
    env_file:
      - .env
    ports:
      - '${CRONS_PORT}:${CRONS_PORT}'
    networks:
      - backend-network
    depends_on:
      kafka-socket:
        condition: service_healthy

networks:
  backend-network:
    driver: bridge        
