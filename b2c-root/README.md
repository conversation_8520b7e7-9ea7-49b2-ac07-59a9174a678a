### Setup Guide

##### Environment Setup

###### 1. Local Setup

1) Install Docker from the following link:
   https://www.docker.com/products/docker-desktop/
2) Create a .env file with the variables in .env.example
3) Choose one of the following:
   a) Manual Setup or b) Automated Setup

###### a) Manual Setup

   i. Clone the repositories of all of the microservices
```sh
   cd ..
   git clone https://github.com/navicater/b2c-backend-service.git
   git clone https://github.com/navicater/b2c-communication-service.git
   git clone https://github.com/navicater/b2c-chat-service.git
   git clone https://github.com/navicater/b2c-crons-service.git
   git clone https://github.com/navicater/b2c-socket-service.git
```   
   ii. Install the packages & generate the Prisma files
```sh
   cd b2c-backend-service
   npm i && npm run generate && cd ..
   
   cd b2c-communication-service
   npm i && npm run generate && cd ..
   
   cd b2c-chat-service
   npm i && npm run generate && cd ..

   cd b2c-crons-service
   npm i && npm run generate && cd ..
   
   cd b2c-socket-service
   npm i
   cd ..
```
   iii. Run the docker cmd:
```sh
cd b2c-root
# Setup containers
docker compose -f docker-compose-local.yaml up
```

###### b) Automated Setup
i. Execute the shell 
 ```sh
   chmod +x setup.sh  # If needed
   ./setup.sh
```