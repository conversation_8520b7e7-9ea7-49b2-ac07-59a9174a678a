API_KEY=
API_MICROSERVICE_KEY=
BACKEND_PORT=
CHAT_PORT=
COMMUNICATION_PORT=
CRONS_PORT=
DO_ACCESS_KEY_ID=
DO_ACCESS_TOKEN=
DO_REGION=
DO_SECRET_KEY=
DO_SIGNED_URL_EXPIRY_S=
DO_SPACES=
DO_SPACES_CDN_ENDPOINT=
DO_SPACES_ENDPOINT=
DO_VM_PORT=
ENCRYPTION_SECRET_KEY=
JWT_SECRET=
KAFKA_BACKEND_CLIENT_ID=
KAFKA_BACKEND_BROKER=
KAFKA_CHAT_CLIENT_ID=
KAFKA_COMMUNICATION_CLIENT_ID=
KAFKA_CRONS_CLIENT_ID=
KAFKA_SOCKET_BROKER=
KAFKA_SOCKET_CLIENT_ID=
MONGO_COMMAND=
MONGO_DATABASE_URL=
MONGO_INITDB_DATABASE=
MONGO_INITDB_ROOT_PASSWORD=
MONGO_INITDB_ROOT_USERNAME=
MONGO_REPLICA_HOST=
MONGO_REPLICA_PORT=
ORG_GITHUB_TOKEN=
POSTGRES_DATABASE_URL=
POSTGRES_DB=
POSTGRES_PASSWORD=
POSTGRES_PORT=
POSTGRES_USER=
SOCKET_PORT=