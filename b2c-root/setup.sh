#   Clone the repositories of all of the microservices
cd ..
git clone https://github.com/navicater/b2c-backend-service.git
git clone https://github.com/navicater/b2c-communication-service.git
git clone https://github.com/navicater/b2c-chat-service.git
git clone https://github.com/navicater/b2c-crons-service.git
git clone https://github.com/navicater/b2c-socket-service.git

# Install the packages & generate the Prisma files
cd b2c-backend-service
npm i && npm run generate && cd ..

cd b2c-communication-service
npm i && npm run generate && cd ..

cd b2c-chat-service
npm i && npm run generate && cd ..

cd b2c-crons-service
npm i && npm run generate && cd ..

cd b2c-socket-service
npm i && cd ..

# Run the docker cmd
cd b2c-root
# Setup containers
docker compose -f docker-compose-local.yaml up