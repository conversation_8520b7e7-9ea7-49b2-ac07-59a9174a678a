name: Release
on:
  push:
    branches: [ prod, dev ]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    name: Release for ${{ github.ref == 'refs/heads/prod' && 'production' || 'development' }}
    steps:
      - name: Checkout b2c-root
        uses: actions/checkout@v4
        with:
          repository: navicater/b2c-root
          path: b2c-root
          token: ${{ secrets.ORG_GITHUB_TOKEN }}

      - name: Checkout b2c-backend-service
        uses: actions/checkout@v4
        with:
          repository: navicater/b2c-backend-service
          path: b2c-backend-service
          token: ${{ secrets.ORG_GITHUB_TOKEN }}
          
      - name: Checkout b2c-communication-service
        uses: actions/checkout@v4
        with:
          repository: navicater/b2c-communication-service
          path: b2c-communication-service
          token: ${{ secrets.ORG_GITHUB_TOKEN }}
          
      - name: Checkout b2c-chat-service
        uses: actions/checkout@v4
        with:
          repository: navicater/b2c-chat-service
          path: b2c-chat-service
          token: ${{ secrets.ORG_GITHUB_TOKEN }}
          
      - name: Checkout b2c-socket-service
        uses: actions/checkout@v4
        with:
          repository: navicater/b2c-socket-service
          path: b2c-socket-service
          token: ${{ secrets.ORG_GITHUB_TOKEN }}
          
      - name: Checkout b2c-crons-service
        uses: actions/checkout@v4
        with:
          repository: navicater/b2c-crons-service
          path: b2c-crons-service
          token: ${{ secrets.ORG_GITHUB_TOKEN }}

      - name: Create a .npmrc
        run:
          echo -e "@navicater:registry=https://npm.pkg.github.com/\n//npm.pkg.github.com/:_authToken=****************************************" > .npmrc

      - name: Copy .npmrc to repositories
        run: |
          cp .npmrc b2c-backend-service/.npmrc
          cp .npmrc b2c-communication-service/.npmrc
          cp .npmrc b2c-chat-service/.npmrc
          cp .npmrc b2c-socket-service/.npmrc
          cp .npmrc b2c-crons-service/.npmrc

      - name: Create .env in b2c-root
        run: |
          cat <<EOF > b2c-root/.env
          API_KEY=${{ secrets.API_KEY }}
          BACKEND_PORT=${{ secrets.BACKEND_PORT }}
          CHAT_PORT=${{ secrets.CHAT_PORT }}
          CRONS_PORT=${{ secrets.CRONS_PORT }}
          COMMUNICATION_PORT=${{ secrets.COMMUNICATION_PORT }}
          DO_ACCESS_KEY_ID=${{ secrets.DO_ACCESS_KEY_ID }}
          DO_ACCESS_TOKEN=${{ secrets.DO_ACCESS_TOKEN }}
          DO_REGION=${{ secrets.DO_REGION }}
          DO_SECRET_KEY=${{ secrets.DO_SECRET_KEY }}
          DO_SIGNED_URL_EXPIRY_S=${{ secrets.DO_SIGNED_URL_EXPIRY_S }}
          DO_SPACES=${{ secrets.DO_SPACES }}
          DO_SPACES_CDN_ENDPOINT=${{ secrets.DO_SPACES_CDN_ENDPOINT }}
          DO_SPACES_ENDPOINT=${{ secrets.DO_SPACES_ENDPOINT }}
          DO_VM_PORT=${{ secrets.DO_VM_PORT }}
          ENCRYPTION_SECRET_KEY=${{ secrets.ENCRYPTION_SECRET_KEY }}
          JWT_SECRET=${{ secrets.JWT_SECRET }}
          KAFKA_BACKEND_CLIENT_ID=${{ secrets.KAFKA_BACKEND_CLIENT_ID }}
          KAFKA_BACKEND_BROKER=${{ secrets.KAFKA_BACKEND_BROKER }}
          KAFKA_CHAT_CLIENT_ID=${{ secrets.KAFKA_CHAT_CLIENT_ID }}
          KAFKA_COMMUNICATION_CLIENT_ID=${{ secrets.KAFKA_COMMUNICATION_CLIENT_ID }}
          KAFKA_CRONS_CLIENT_ID=${{ secrets.KAFKA_CRONS_CLIENT_ID }}
          KAFKA_SOCKET_BROKER=${{ secrets.KAFKA_SOCKET_BROKER }}
          KAFKA_SOCKET_CLIENT_ID=${{ secrets.KAFKA_SOCKET_CLIENT_ID }}
          MONGO_COMMAND=${{ secrets.MONGO_COMMAND }}
          MONGO_DATABASE_URL=${{ secrets.MONGO_DATABASE_URL }}
          MONGO_INITDB_DATABASE=${{ secrets.MONGO_INITDB_DATABASE }}
          MONGO_INITDB_ROOT_PASSWORD=${{ secrets.MONGO_INITDB_ROOT_PASSWORD }}
          MONGO_INITDB_ROOT_USERNAME=${{ secrets.MONGO_INITDB_ROOT_USERNAME }}
          MONGO_REPLICA_HOST=${{ secrets.MONGO_REPLICA_HOST }}
          MONGO_REPLICA_PORT=${{ secrets.MONGO_REPLICA_PORT }}
          ORG_GITHUB_TOKEN=${{ secrets.ORG_GITHUB_TOKEN }}
          POSTGRES_DATABASE_URL=${{ secrets.POSTGRES_DATABASE_URL }}
          POSTGRES_DB=${{ secrets.POSTGRES_DB }}
          POSTGRES_HOST=${{ secrets.POSTGRES_HOST }}
          POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}
          POSTGRES_PORT=${{ secrets.POSTGRES_PORT }}
          POSTGRES_USER=${{ secrets.POSTGRES_USER }}
          SOCKET_PORT=${{ secrets.SOCKET_PORT }}
          API_MICROSERVICE_KEY=${{ secrets.API_MICROSERVICE_KEY }}
          EOF

      - name: Set environment variables based on branch
        run: |
          if [ "${{ github.ref }}" = "refs/heads/prod" ]; then
            echo "DO_VM_HOST=${{ secrets.DO_VM_HOST }}" >> $GITHUB_ENV
            echo "DO_SSH_PRIVATE_KEY<<EOF" >> $GITHUB_ENV
            echo "${{ secrets.DO_SSH_PRIVATE_KEY }}" >> $GITHUB_ENV
            echo "EOF" >> $GITHUB_ENV
          else
            echo "DO_VM_HOST=${{ secrets.DO_VM_HOST_DEV }}" >> $GITHUB_ENV
            echo "DO_SSH_PRIVATE_KEY<<EOF" >> $GITHUB_ENV
            echo "${{ secrets.DO_SSH_PRIVATE_KEY_DEV }}" >> $GITHUB_ENV
            echo "EOF" >> $GITHUB_ENV
          fi

      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ env.DO_SSH_PRIVATE_KEY }}
          known_hosts: unnecessary

      - name: Compress & Transfer the files
        run: |
          mkdir -p staging
          cp -r b2c-root b2c-backend-service b2c-communication-service b2c-chat-service b2c-socket-service b2c-crons-service staging/

          tar -czf b2c-deployment.tar.gz -C staging \
            --exclude='.git' \
            --exclude='.github' \
            --exclude='*.log' \
            --exclude='node_modules' \
            --exclude='dist' \
            --exclude='build' \
            --exclude='*.tmp' \
            b2c-root b2c-backend-service b2c-communication-service b2c-chat-service b2c-socket-service b2c-crons-service

          rsync -avz --progress -e "ssh -o StrictHostKeyChecking=no" b2c-deployment.tar.gz root@${{ env.DO_VM_HOST }}:/app/ || { echo "rsync failed"; exit 1; }
      
          ssh -o StrictHostKeyChecking=no root@${{ env.DO_VM_HOST }} << 'EOF'
            mkdir -p /app
            rm -rf /app/b2c-root /app/b2c-backend-service /app/b2c-communication-service /app/b2c-chat-service /app/b2c-socket-service /app/b2c-crons-service
            tar -xzf /app/b2c-deployment.tar.gz -C /app/ || { echo "tar extraction failed"; exit 1; }
            rm /app/b2c-deployment.tar.gz
          EOF

          rm -rf b2c-deployment.tar.gz staging

      - name: Install Docker on VM if missing
        run: |
          ssh -o StrictHostKeyChecking=no root@${{ env.DO_VM_HOST }} << 'EOF'
            # Check if Docker is installed
            if ! command -v docker &> /dev/null; then
              echo "Installing Docker..."
              # Official Docker installation script
              curl -fsSL https://get.docker.com -o get-docker.sh
              sh get-docker.sh
              rm get-docker.sh
              
              # Install Docker Compose v2
              mkdir -p ~/.docker/cli-plugins/
              curl -SL https://github.com/docker/compose/releases/latest/download/docker-compose-linux-x86_64 -o ~/.docker/cli-plugins/docker-compose
              chmod +x ~/.docker/cli-plugins/docker-compose
              
              # Enable non-root user access (if needed)
              sudo usermod -aG docker $USER
              newgrp docker
              
              # Verify installation
              docker --version
              docker compose version
            else
              echo "Docker is already installed"
              docker --version
            fi
          EOF

      - name: Deploy the microservices
        run: |
          ssh -o StrictHostKeyChecking=no -o ServerAliveInterval=60 root@${{ env.DO_VM_HOST }}<<'EOF'
            # Navigate to your app directory
            cd /app/b2c-root || { echo "Failed to find /app/b2c-root"; exit 1; }
            docker compose -f docker-compose-prod.yaml down
            docker system prune -af
            docker compose -f docker-compose-prod.yaml up -d --build
            sleep 10
            if ! docker compose -f docker-compose-prod.yaml ps | grep -q "Up"; then
              echo "Services failed to start"
              exit 1
            fi
            docker image prune -f
          EOF
