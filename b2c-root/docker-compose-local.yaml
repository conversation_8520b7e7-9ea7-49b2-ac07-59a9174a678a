name: root
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.9.1
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    networks:
      - backend-network
    healthcheck:
      test: ["CMD-SHELL", "nc -z localhost 2181 || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3

  kafka-backend:  
    image: confluentinc/cp-kafka:7.9.1
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-backend:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 3000
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    ports:
      - "9092:9092"
    networks:
      - backend-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "kafka-backend:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5

  kafka-socket:
      image: confluentinc/cp-kafka:7.9.1
      depends_on:
        zookeeper:
          condition: service_healthy
      environment:
        KAFKA_BROKER_ID: 2
        KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
        KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-socket:9093
        KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
        KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
        KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 3000
        KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
        KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      ports:
        - "9093:9093"
      networks:
        - backend-network
      healthcheck:
        test: ["CMD", "kafka-topics", "--bootstrap-server", "kafka-socket:9093", "--list"]
        interval: 30s
        timeout: 10s
        retries: 5

  backend_mongodb:
    image: mongo:6
    container_name: backend_mongodb
    env_file:
      - .env
    ports:
      - '${MONGO_REPLICA_PORT}:27019'
    restart: always
    volumes:
      - ../b2c-backend-service/scripts/init-mongo.sh:/init-mongo.sh
      - backend_mongodb_data:/data/db
    entrypoint: ['bash', './init-mongo.sh']
    healthcheck:
      test: [
          'CMD-SHELL',
          'mongosh --port $$MONGO_REPLICA_PORT --eval
          "db.adminCommand(''ping'')"',
        ]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - backend-network

  backend_postgres_db:
    platform: linux/amd64
    image: postgis/postgis:17-3.5-alpine
    container_name: backend_postgres_db
    env_file:
      - .env
    ports:
      - '${POSTGRES_PORT}:5432'
    volumes:
      - backend_postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - backend-network

  backend-service:
    build: 
      context: ../b2c-backend-service
      dockerfile: Dockerfile.local
    env_file:
      - .env
    ports:
      - '${BACKEND_PORT}:${BACKEND_PORT}'
    networks:
      - backend-network
    volumes:
      - ../b2c-backend-service:/app
      - /app/node_modules
    depends_on:
      backend_postgres_db:
        condition: service_healthy
      backend_mongodb:
        condition: service_healthy
      kafka-backend:
        condition: service_started

  communication-service:
    build:
      context: ../b2c-communication-service
      dockerfile: ./Dockerfile.local
    env_file:
      - .env
    ports:
      - '${COMMUNICATION_PORT}:${COMMUNICATION_PORT}'
    networks:
      - backend-network
    volumes:
      - ../b2c-communication-service:/app
      - /app/node_modules
    depends_on:
      backend_mongodb:
        condition: service_healthy
      kafka-backend:
        condition: service_started

  chat-service:
    build:
      context: ../b2c-chat-service
      dockerfile: ./Dockerfile.local
    env_file:
      - .env
    ports:
      - '${CHAT_PORT}:${CHAT_PORT}'
    volumes:
      - ../b2c-chat-service:/app
      - /app/node_modules
    networks:
      - backend-network
    depends_on:
      backend_mongodb:
        condition: service_healthy
      kafka-socket:
        condition: service_healthy

  socket-service:
    build:
      context: ../b2c-socket-service
      dockerfile: ./Dockerfile.local
    env_file:
      - .env
    ports:
      - '${SOCKET_PORT}:${SOCKET_PORT}'
    volumes:
      - ../b2c-socket-service:/app
      - /app/node_modules
    networks:
      - backend-network
    depends_on:
      kafka-socket:
        condition: service_healthy

  crons-service:
    build:
      context: ../b2c-crons-service
      dockerfile: ./Dockerfile.local
    env_file:
      - .env
    ports:
      - '${CRONS_PORT}:${CRONS_PORT}'
    volumes:
      - ../b2c-crons-service:/app
      - /app/node_modules
    networks:
      - backend-network
    depends_on:
      backend_postgres_db:
        condition: service_healthy
      backend_mongodb:
        condition: service_healthy
      kafka-socket:
        condition: service_healthy

volumes:
  backend_postgres_data:
  backend_mongodb_data:

networks:
  backend-network:
    driver: bridge
  