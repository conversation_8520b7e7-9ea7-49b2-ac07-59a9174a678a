import type { FastifyInstance, FastifyReply } from 'fastify';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import { AnnouncementModule } from '@modules/announcement';
import { CursorPaginationSchema } from '@schemas/common/common';
import { NearByFetchPeopleBodySchema } from '@schemas/announcement/nearBy';
import Auth from '@modules/auth';

const nearByRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/near-by', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = CursorPaginationSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PFL008', queryError);
    }

    const { data: bodyData, error: bodyError } = NearByFetchPeopleBodySchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('PFL008', { error: bodyError.errors });
    }
    const result = await AnnouncementModule.NearByModule.fetchPeople(request, { ...bodyData, ...queryData });
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.patch('/backend/api/v1/auth/session/anonymous', async (request: FastifyRequestI, reply: FastifyReply) => {
    await Auth.SessionModule.updateAnonymity(request);
    reply.status(HttpStatus.OK);
  });
  fastify.get('/backend/api/v1/auth/session/anonymous', async (request: FastifyRequestI, reply: FastifyReply) => {
    const result = await Auth.SessionModule.fetchAnonymityStatus(request);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default nearByRoutes;
