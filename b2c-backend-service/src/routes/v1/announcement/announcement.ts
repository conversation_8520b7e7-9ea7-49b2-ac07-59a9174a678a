import { FastifyInstance, FastifyReply } from 'fastify';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';

import { AnnouncementCreateOneSchema, AnnouncementDeleteSchema, CoreAnnouncementFetchParamsSchema } from '@schemas/announcement/announcement';
import { AnnouncementModule } from '@modules/announcement';

const coreAnnouncementRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/announcement', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data: body } = AnnouncementCreateOneSchema.safeParse(request.body);
    if (error) {
      throw new AppError('ANC006', error);
    }
    const result = await AnnouncementModule.CoreAnnouncementModule.createOne(request, body);
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.post('/backend/api/v1/announcements/fetch', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data: params } = CoreAnnouncementFetchParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('ANC007', error);
    }
    const result = await AnnouncementModule.CoreAnnouncementModule.fetchAnnouncements(request, params);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.delete('/backend/api/v1/announcements', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data: params } = AnnouncementDeleteSchema.safeParse(request.query);
    if (error) {
      throw new AppError('ANC007', error);
    }
    await AnnouncementModule.CoreAnnouncementModule.deleteOne(request, params);
    reply.status(HttpStatus.NO_CONTENT);
  });
};

export default coreAnnouncementRoutes;
