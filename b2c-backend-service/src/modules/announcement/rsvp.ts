import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { rsvpFetchPeopleItemI } from '@interfaces/announcement/rsvp';
import { CursorDataI } from '@interfaces/common/data';
import type { FastifyStateI } from '@interfaces/common/declaration';
import { ProfileExternalI } from '@interfaces/user/profile';
import User from '@modules/user';
import { Prisma } from '@prisma/postgres';
import { RSVPFetchPeopleI, RSVPUpsertInputI } from '@schemas/announcement/rsvp';

export const RSVPModule = {
  upsert: async (state: FastifyStateI, { id: announcementId, status }: RSVPUpsertInputI): Promise<void> => {
    const selfProfileId = state.profileId;
    if (!selfProfileId) {
      throw new AppError('PFL001');
    }
    const announcementExists = await prismaPG.announcement.findUnique({
      where: { id: announcementId },
      select: { id: true },
    });
    if (!announcementExists) {
      throw new AppError('RSVP002');
    }
    const existing = await prismaPG.rSVP.findUnique({
      where: {
        profileId_announcementId: {
          profileId: selfProfileId,
          announcementId,
        },
      },
    });

    if (existing) {
      if (status === 'ATTEND') {
        throw new AppError('RSVP001');
      } else if (status === 'UNATTEND') {
        await prismaPG.rSVP.delete({
          where: {
            profileId_announcementId: {
              profileId: selfProfileId,
              announcementId,
            },
          },
        });
      }
    } else {
      if (status === 'UNATTEND') {
        throw new AppError('RSVP004');
      } else if (status === 'ATTEND') {
        await prismaPG.rSVP.create({
          data: {
            profileId: selfProfileId,
            announcementId,
          },
        });
      }
    }
  },
  fetchProfiles: async (
    state: FastifyStateI,
    { id: announcementId, cursorId, pageSize }: RSVPFetchPeopleI,
  ): Promise<CursorDataI<rsvpFetchPeopleItemI>> => {
    const selfProfileId = state.profileId;

    const filters: Prisma.RSVPWhereInput = {
      announcementId,
      Profile: {
        status: 'ACTIVE',
      },
      NOT: {
        Profile: {
          OR: [
            {
              BlockedByProfile: {
                some: { blockerId: selfProfileId },
              },
            },
            {
              BlockedProfile: {
                some: { blockedId: selfProfileId },
              },
            },
          ],
        },
      },
    };

    const rsvpResultTemp = await prismaPG.rSVP.findMany({
      where: {
        ...filters,
        ...(typeof cursorId === 'number' && cursorId > 0 ? { cursorId: { lt: BigInt(cursorId) } } : {}),
      },
      select: {
        cursorId: true,
        profileId: true,
        announcementId: true,
        Profile: {
          select: {
            id: true,
            name: true,
            avatar: true,
            designationText: true,
            designationAlternativeId: true,
            designationRawDataId: true,
            entityText: true,
            entityId: true,
            entityRawDataId: true,
            Connections:{
              select:{
                connectedId:true
              }
            },
            ConnectedId: true
          },
        },
      },
      orderBy: { cursorId: 'desc' },
      take: pageSize + 1,
    });
    const hasNextPage = rsvpResultTemp.length > pageSize;
    const results = hasNextPage ? rsvpResultTemp.slice(0, pageSize) : rsvpResultTemp;

    const rsvpProfiles: rsvpFetchPeopleItemI[] = [];
    let nextCursorId = null;

    if (results?.length) {
      rsvpProfiles.push(
      ...results.map((rsvp) => {
        let status = "NOT_CONNECTED";
        if (
          rsvp.Profile.Connections?.some(c => c.connectedId === selfProfileId) ||
          rsvp.Profile.ConnectedId?.some(c => c.profileId === selfProfileId)
        ) {
          status = "CONNECTED";
        }

        return {
          Profile:User.ProfileModule.transformProfile(rsvp.Profile) as unknown as ProfileExternalI,
          cursorId: rsvp.cursorId.toString(),
          status: status as "CONNECTED" | "NOT_CONNECTED"
        };
      })
    );
    nextCursorId = hasNextPage ? rsvpProfiles.length > 0 ? rsvpProfiles[rsvpProfiles.length - 1].cursorId : null : null
  }

    return { data: rsvpProfiles, nextCursorId };
  },
};
