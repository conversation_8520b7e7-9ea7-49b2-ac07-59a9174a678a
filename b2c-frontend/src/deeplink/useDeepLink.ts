import { useEffect, useRef } from 'react';
import { Linking } from 'react-native';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import { URL } from 'react-native-url-polyfill';
import { useSelector } from 'react-redux';
import type { BottomTabNavigationI, RootDrawerParamListI } from '../navigation/types';
import { selectCurrentUser } from '../redux/selectors/user';
import { validateRoute } from './deeplink-validators';
import type { Route, RouteConfig } from './types';

const routeConfig: RouteConfig = {
  post: { stack: 'HomeStack', screen: 'Comment', paramKeys: ['type', 'postId'] },
  profile: {
    stack: 'HomeStack',
    screen: 'OtherUserProfile',
    paramKeys: ['profileId', 'fromTabPress'],
  },
  forum: { stack: 'LearnCollabStack', screen: 'ForumAnswers', paramKeys: ['postId'] },
  ship: { stack: 'HomeStack', screen: 'ShipProfile', paramKeys: ['imo', 'dataType'] },
  port: { stack: 'HomeStack', screen: 'PortProfile', paramKeys: ['unLocode', 'dataType'] },
} as const;

export const useDeepLink = () => {
  const currentUser = useSelector(selectCurrentUser);
  const navigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();

  const pendingUrlRef = useRef<string | null>(null);
  const isNavigationReadyRef = useRef(false);

  const handleUrl = async (url: string | null) => {
    if (!url) return;
    if (!isNavigationReadyRef.current) {
      pendingUrlRef.current = url;
      return;
    }

    let path: string;
    if (url.startsWith('navicater://')) {
      path = url.replace(/.*?:\/\//g, '');
    } else {
      const urlObj = new URL(url);
      path = urlObj.pathname.startsWith('/') ? urlObj.pathname.substring(1) : urlObj.pathname;
    }

    const segments = path.split('/');
    const route = segments[0] as Route;
    const config = routeConfig[route];

    if (!config) {
      navigation.navigate('HomeStack', { screen: 'NotFound' });
      return;
    }

    const params = config.paramKeys.reduce(
      (acc, key, i) => {
        acc[key] = segments[i + 1] || '';
        return acc;
      },
      {} as Record<string, string>,
    );

    if (route === 'profile' && currentUser && params.profileId === currentUser.profileId) {
      drawerNavigation.navigate('ProfileStack', {
        screen: 'UserProfile',
        params: { fromTabPress: true, profileId: params.profileId },
      });
      return;
    }

    const validation = await validateRoute(route, params);
    if (!validation.isValid) {
      navigation.navigate('HomeStack', { screen: 'NotFound' });
      return;
    }

    if (route === 'post') {
      navigation.navigate('HomeStack', {
        screen: 'Comment',
        params: { postId: params.postId, type: params.type as any },
      });
    } else if (route === 'forum') {
      navigation.navigate('LearnCollabStack', {
        screen: 'ForumAnswers',
        params: { postId: params.postId },
      });
    } else if (route === 'ship') {
      navigation.navigate('HomeStack', {
        screen: 'ShipProfile',
        params: { imo: params.imo, dataType: params.dataType },
      });
    } else if (route === 'port') {
      navigation.navigate('HomeStack', {
        screen: 'PortProfile',
        params: { unLocode: params.unLocode, dataType: params.dataType },
      });
    } else if (route === 'profile') {
      navigation.navigate('HomeStack', {
        screen: 'OtherUserProfile',
        params: { profileId: params.profileId, fromTabPress: false },
      });
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      isNavigationReadyRef.current = true;
      if (pendingUrlRef.current) {
        const url = pendingUrlRef.current;
        pendingUrlRef.current = null;
        handleUrl(url);
      }
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    Linking.getInitialURL().then(handleUrl);
    const sub = Linking.addListener('url', ({ url }) => handleUrl(url));
    return () => sub.remove();
  }, []);
};
