import type React from 'react';
import { View, Text, Switch, ScrollView, ActivityIndicator, Pressable } from 'react-native';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { twMerge } from 'tailwind-merge';
import Accordion from '@/src/components/Accordion';
import BackButton from '@/src/components/BackButton';
import CustomModal from '@/src/components/Modal';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type { RootDrawerParamListI } from '@/src/navigation/types';
import { version } from '@/package.json';
import AcceptTerms from '../AcceptTerms';
import type {
  SettingsSectionProps,
  SettingsItemProps,
  ToggleSettingProps,
  UserSettingsProps,
} from './types';
import { useSettings } from './useHook';

const SettingsSection: React.FC<SettingsSectionProps> = ({ title, children }) => {
  return (
    <View className="mb-6">
      <Text className="text-base font-semibold text-gray-800 mb-2">{title}</Text>
      <View className="bg-white rounded-xl overflow-hidden">{children}</View>
    </View>
  );
};

const SettingsItem: React.FC<SettingsItemProps> = ({
  title,
  description,
  icon,
  rightElement,
  onPress,
  isLast = false,
  isDanger = false,
}) => {
  return (
    <Pressable
      onPress={onPress}
      disabled={!onPress}
      className={`flex-row items-center px-4 py-4 ${!isLast ? 'border-b border-gray-100' : ''}`}
    >
      {icon && <View className="mr-3">{icon}</View>}
      <View className="flex-1">
        <Text
          className={twMerge('text-sm font-medium', isDanger ? 'text-red-700' : 'text-gray-800')}
        >
          {title}
        </Text>
        {description && <Text className="text-xs text-gray-500 mt-1">{description}</Text>}
      </View>
      {rightElement && <View>{rightElement}</View>}
    </Pressable>
  );
};

const ToggleSetting: React.FC<ToggleSettingProps> = ({
  title,
  description,
  icon,
  value,
  onValueChange,
  isLast,
}) => {
  return (
    <SettingsItem
      title={title}
      description={description}
      icon={icon}
      isLast={isLast}
      rightElement={
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#D1D5DB', true: '#4CAF50' }}
          thumbColor="#FFFFFF"
        />
      }
    />
  );
};

const UserSettingsMenu: React.FC<UserSettingsProps> = ({ onBack }) => {
  const currentUser = useSelector(selectCurrentUser);
  const navigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const {
    isLoading,
    signOut,
    deleteAccount,
    deactivateAccount,
    isSignOutModalVisible,
    isDeleteAccountModalVisible,
    isDeactivateAccountModalVisible,
    handleConfirmSignOut,
    handleCancelSignOut,
    handleConfirmDeleteAccount,
    handleCancelDeleteAccount,
    handleConfirmDeactivateAccount,
    handleCancelDeactivateAccount,
    isDeleting,
    handleTermsAccepted,
    termsAccepted,
  } = useSettings(currentUser.profileId);

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50">
        <ActivityIndicator size="small" color="#4CAF50" />
        <Text className="mt-4 text-gray-600">Loading settings...</Text>
      </View>
    );
  }

  return (
    <>
      <CustomModal
        isVisible={isSignOutModalVisible}
        title="Sign Out"
        description="Are you sure you want to sign out?"
        confirmText="Sign Out"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={handleConfirmSignOut}
        onCancel={handleCancelSignOut}
        cancelButtonMode="default"
        confirmButtonMode="outlined"
      />
      <CustomModal
        isVisible={isDeleteAccountModalVisible}
        title="Delete Account"
        description="Are you sure you want to delete your account?  (It will be permanently deleted in 30 days)"
        confirmText="Delete"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={handleConfirmDeleteAccount}
        onCancel={handleCancelDeleteAccount}
        isConfirming={isDeleting}
        disableConfirm={termsAccepted === false}
        confirmButtonMode="outlined"
        cancelButtonMode="default"
        bodyComponent={<AcceptTerms onTermsAccepted={handleTermsAccepted} />}
      />
      <CustomModal
        isVisible={isDeactivateAccountModalVisible}
        title="Deactivate Account"
        description="Are you sure you want to deactivate your account?"
        confirmText="Deactivate"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={handleConfirmDeactivateAccount}
        onCancel={handleCancelDeactivateAccount}
        isConfirming={isDeleting}
        disableConfirm={termsAccepted === false}
        confirmButtonMode="outlined"
        cancelButtonMode="default"
        bodyComponent={<AcceptTerms onTermsAccepted={handleTermsAccepted} />}
      />
      <BackButton onBack={onBack} label="" />
      <ScrollView className="flex-1 px-4 py-6 bg-gray-50">
        <SettingsSection title="Account">
          <SettingsItem
            title="Profile Information"
            description="Update your personal information"
            onPress={() => navigation.navigate('ProfileStack', { screen: 'EditUserProfile' })}
          />
        </SettingsSection>
        <SettingsSection title="Privacy">
          <SettingsItem
            title="Blocked Profiles"
            description="Manage profiles you have blocked"
            onPress={() => navigation.navigate('ProfileStack', { screen: 'BlockedUserProfiles' })}
            isLast
          />
        </SettingsSection>
        <SettingsSection title="Support">
          <SettingsItem
            title="Terms of Service"
            description="Read our terms and conditions"
            onPress={() => navigation.navigate('ProfileStack', { screen: 'Terms' })}
          />
          <SettingsItem
            title="Privacy Policy"
            description="Read our privacy policy"
            onPress={() => navigation.navigate('ProfileStack', { screen: 'Privacy' })}
            isLast
          />
        </SettingsSection>
        <Accordion
          title="Account Control"
          hideBorder
          contentClassName="-my-4"
          content={
            <SettingsSection title="">
              <SettingsItem
                title="Deactivate Account"
                description="Deactivate your account from Navicater"
                onPress={deactivateAccount}
              />
              <SettingsItem
                title="Delete Account"
                description="Delete your account from Navicater"
                onPress={deleteAccount}
                isDanger
              />
            </SettingsSection>
          }
        />
        <Accordion
          title="Session Management"
          contentClassName="-my-4"
          hideBorder
          content={
            <SettingsSection title="">
              <SettingsItem
                title="Sign Out"
                description="Log out of your account"
                onPress={signOut}
              />
            </SettingsSection>
          }
        />
        <View className="py-6 items-center">
          <Text className="text-gray-500 text-sm">Version {version}</Text>
        </View>
      </ScrollView>
    </>
  );
};

export default UserSettingsMenu;
