import type React from 'react';
import { useState, useEffect } from 'react';
import {
  TextInput,
  View,
  Text,
  Pressable,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Tabs from '@/src/components/Tabs';
import { resetSearchPosts, setPopularPosts } from '@/src/redux/slices/content/contentSlice';
import { addSearch } from '@/src/redux/slices/globalsearch/globalSearchSlice';
import type { AppDispatch, RootState } from '@/src/redux/store';
import { fetchGlobalSearchResults } from '@/src/utilities/search/globalSearch';
import type { GlobalSearchCategory, GlobalSearchResponse } from '@/src/utilities/search/types';
import { popularPeopleGlobalAPI } from '@/src/networks/connect/search';
import { popularPostGlobalAPI } from '@/src/networks/content/post';
import { PostExternalClientI } from '@/src/networks/content/types';
import { popularPortGlobalAPI } from '@/src/networks/port/search';
import { popularShipGlobalAPI } from '@/src/networks/ship/search';
import PopularResults from '../PopularResults';
import RecentSearches from '../RecentSearches';
import SearchResults from '../SearchResults';
import type { SearchCategoryTabs, SearchResultItemI } from '../SearchResults/types';
import type { SearchBoxPropsI } from './types';

const tabs: SearchCategoryTabs[] = [
  { id: 'people', label: 'People' },
  { id: 'post', label: 'Posts' },
  { id: 'ship', label: 'Ship' },
  { id: 'port', label: 'Port' },
  { id: 'organization', label: 'Organization' },
  { id: 'institution', label: 'Institution' },
];

const Searchbox: React.FC<SearchBoxPropsI> = ({ onBack, onError }) => {
  const [searchData, setSearchData] = useState<string>('');
  const [showRecent, setShowRecent] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<GlobalSearchCategory>('people');
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<
    Record<GlobalSearchCategory, GlobalSearchResponse>
  >({
    people: { data: [], total: 0 },
    post: { data: [], total: 0 },
    ship: { data: [], total: 0 },
    port: { data: [], total: 0 },
    organization: { data: [], total: 0 },
    institution: { data: [], total: 0 },
  });
  const [currentPage, setCurrentPage] = useState<Record<GlobalSearchCategory, number>>({
    people: 0,
    post: 0,
    ship: 0,
    port: 0,
    organization: 0,
    institution: 0,
  });
  const [lastSearchQuery, setLastSearchQuery] = useState<string>('');
  const [popularSuggestionsData, setPopularSuggestionsData] = useState<
    Record<GlobalSearchCategory, (SearchResultItemI | PostExternalClientI)[]>
  >({
    people: [],
    post: [],
    ship: [],
    port: [],
    organization: [],
    institution: [],
  });
  const [loadingPopularSuggestions, setLoadingPopularSuggestions] = useState(false);
  const dispatch = useDispatch<AppDispatch>();

  const resetSearchState = () => {
    setSearchData('');
    setSearchResults({
      people: { data: [], total: 0 },
      post: { data: [], total: 0 },
      ship: { data: [], total: 0 },
      port: { data: [], total: 0 },
      organization: { data: [], total: 0 },
      institution: { data: [], total: 0 },
    });
    setShowRecent(true);
    setLastSearchQuery('');
  };

  const performSearch = async (query: string, category: GlobalSearchCategory, page = 0) => {
    if (!query.trim()) {
      setSearchResults((prev) => ({
        ...prev,
        [category]: { data: [], total: 0 },
      }));
      return;
    }
    try {
      const results = await fetchGlobalSearchResults(category, query, page);
      if (results) {
        setSearchResults((prev) => ({
          ...prev,
          [category]:
            page === 0
              ? results
              : { data: [...prev[category].data, ...results.data], total: results.total },
        }));
      }
    } catch (error) {
      const errorMessage = `Failed to search ${category}: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
      setSearchResults((prev) => ({
        ...prev,
        [category]: { data: [], total: 0 },
      }));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchPopularSuggestions = async () => {
    setLoadingPopularSuggestions(true);
    try {
      const [people, posts, ships, ports] = await Promise.all([
        popularPeopleGlobalAPI(),
        popularPostGlobalAPI(),
        popularShipGlobalAPI(),
        popularPortGlobalAPI(),
      ]);
      dispatch(setPopularPosts({ posts: posts.data as PostExternalClientI[], append: true }));
      setPopularSuggestionsData({
        people: people.data.slice(0, 5) as SearchResultItemI[],
        post: posts.data.slice(0, 5) as PostExternalClientI[],
        ship: ships.data.slice(0, 5) as SearchResultItemI[],
        port: ports.data.slice(0, 5) as SearchResultItemI[],
        organization: [],
        institution: [],
      });
    } catch (error) {
      const errorMessage = `Failed to fetch popular suggestions: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
      setPopularSuggestionsData({
        people: [],
        post: [],
        ship: [],
        port: [],
        organization: [],
        institution: [],
      });
    } finally {
      setLoadingPopularSuggestions(false);
    }
  };

  useEffect(() => {
    if (showRecent && searchData.trim() === '') {
      fetchPopularSuggestions();
    }
  }, [showRecent, searchData]);

  const handleSubmit = async () => {
    const trimmed = searchData.trim();
    if (trimmed) {
      setLoading(true);
      setCurrentPage((prev) => ({ ...prev, [activeTab]: 0 }));
      setLastSearchQuery(trimmed);
      dispatch(addSearch({ searchText: trimmed, category: activeTab }));
      setShowRecent(false);
      await performSearch(trimmed, activeTab, 0);
    }
  };

  const handleTabChange = (tab: GlobalSearchCategory) => {
    setActiveTab(tab);
    setCurrentPage((prev) => ({ ...prev, [tab]: 0 }));
    dispatch(resetSearchPosts());
    if (lastSearchQuery.trim()) {
      setShowRecent(false);
      if (!searchResults[tab].data.length || lastSearchQuery !== searchData.trim()) {
        setLoading(true);
        performSearch(lastSearchQuery.trim(), tab, 0);
      }
    } else {
      setShowRecent(true);
    }
  };

  const handleSearchChange = (text: string) => {
    setSearchData(text);
    if (text.trim() === '') {
      resetSearchState();
    } else {
      if (!lastSearchQuery.trim() || lastSearchQuery.trim() !== text.trim()) {
        setShowRecent(true);
      }
    }
  };

  const handleLoadMore = () => {
    if (
      loading ||
      !lastSearchQuery.trim() ||
      searchResults[activeTab].data.length === searchResults[activeTab].total
    )
      return;
    const nextPage = currentPage[activeTab] + 1;
    setCurrentPage((prev) => ({ ...prev, [activeTab]: nextPage }));
    setLoading(true);
    performSearch(lastSearchQuery, activeTab, nextPage);
  };

  const handleRefresh = async () => {
    if (!lastSearchQuery.trim()) return;
    setRefreshing(true);
    setCurrentPage((prev) => ({ ...prev, [activeTab]: 0 }));
    await performSearch(lastSearchQuery, activeTab, 0);
  };

  const recentSearches = useSelector((state: RootState) =>
    state.globalsearch.recentSearches.filter((item) => item.category === activeTab),
  ).slice(0, 3);
  const hasRecentSearches = recentSearches.length > 0;

  return (
    <View className="flex-1 bg-white">
      <View className="px-4">
        <View className="flex-row items-center space-x-3">
          <BackButton onBack={onBack} label="" />
          <View className="flex-1 flex-row items-center bg-gray-100 rounded-xl px-3">
            <TextInput
              autoFocus
              placeholder="Search for name or @username"
              placeholderTextColor="#6b7280"
              value={searchData}
              onChangeText={handleSearchChange}
              onSubmitEditing={handleSubmit}
              returnKeyType="search"
              className="flex-1 text-black pt-3 pb-4"
            />
            {searchData.length > 0 && (
              <Pressable onPress={resetSearchState} className="p-1">
                <Text className="text-gray-500">✕</Text>
              </Pressable>
            )}
          </View>
        </View>
      </View>
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <View className="flex-1">
          <View className="pt-4">
            <Tabs
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={(tab) => {
                handleTabChange(tab as GlobalSearchCategory);
              }}
              disabled={loading}
            />
          </View>
          {showRecent ? (
            <View className="flex-1">
              {hasRecentSearches && (
                <View className="h-full">
                  <RecentSearches
                    setSearchData={setSearchData}
                    setActiveTab={setActiveTab}
                    setLoading={setLoading}
                    setShowRecent={setShowRecent}
                    performSearch={performSearch}
                    setLastSearchQuery={setLastSearchQuery}
                    category={activeTab}
                  />
                </View>
              )}
              {!hasRecentSearches && activeTab !== 'organization' && activeTab !== 'institution' ? (
                loadingPopularSuggestions ? (
                  <View className="justify-center items-center py-4">
                    <ActivityIndicator size="small" />
                  </View>
                ) : (
                  <PopularResults
                    suggestions={popularSuggestionsData[activeTab]}
                    activeTab={activeTab}
                  />
                )
              ) : (
                !hasRecentSearches &&
                (activeTab === 'organization' || activeTab === 'institution') && (
                  <View className="flex-1 justify-center items-center px-8">
                    <View
                      style={{ width: 100, height: 100 }}
                      className="bg-gray-100 rounded-full items-center justify-center mb-6"
                    >
                      <Text className="text-4xl">🔍</Text>
                    </View>
                    <Text className="text-xl font-semibold text-gray-800 text-center mb-2">
                      No searches yet
                    </Text>
                    <Text className="text-gray-500 text-center leading-relaxed">
                      Start searching for something to see results.
                    </Text>
                  </View>
                )
              )}
            </View>
          ) : (
            <SearchResults
              activeTab={activeTab}
              searchResults={searchResults[activeTab]}
              loading={loading}
              onLoadMore={handleLoadMore}
              searchText={lastSearchQuery}
              refreshing={refreshing}
              onRefresh={handleRefresh}
              onError={onError}
            />
          )}
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

export default Searchbox;
