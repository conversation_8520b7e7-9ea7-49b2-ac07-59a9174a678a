import type React from 'react';
import { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { useSelector } from 'react-redux';
import type { RootState } from '@/src/redux/store';
import type { GlobalSearchCategory } from '@/src/utilities/search/types';
import Clock from '@/src/assets/svgs/Clock';
import type { RecentSearchesProps } from './types';

const RecentSearches: React.FC<RecentSearchesProps> = ({
  setSearchData,
  setShowRecent,
  setActiveTab,
  setLoading,
  performSearch,
  setLastSearchQuery,
  category,
}) => {
  const recentSearches = useSelector((state: RootState) =>
    state.globalsearch.recentSearches.filter((item) => item.category === category),
  ).slice(0, 3);
  const [error, setError] = useState<Error | null>(null);
  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };
  if (error) {
    throw error;
  }
  const onPress = async (item: { category: string; searchText: string }) => {
    try {
      setSearchData(item.searchText);
      setActiveTab(item.category as GlobalSearchCategory);
      setShowRecent(false);
      setLoading(true);
      setLastSearchQuery(item.searchText);
      await performSearch(item.searchText, item.category as GlobalSearchCategory, 0);
    } catch (error) {
      const errorMessage = `Failed to perform recent search: ${error instanceof Error ? error.message : 'Unknown error'}`;
      triggerErrorBoundary(new Error(errorMessage));
    }
  };

  return (
    <FlatList
      data={recentSearches}
      keyExtractor={(item, index) => `${item.searchText}-${index}`}
      renderItem={({ item }) => (
        <Pressable onPress={() => onPress(item)} className="flex-row items-center pb-2 px-4 mt-1">
          <View className="items-center justify-center mr-3">
            <Clock />
          </View>
          <View className="flex-row gap-2">
            <Text className="font-medium text-gray-800 text-base">{item.searchText}</Text>
            <Text className="font-light text-gray-800 text-base">in {item.category}</Text>
          </View>
        </Pressable>
      )}
      contentContainerStyle={{
        backgroundColor: 'white',
        paddingVertical: 10,
      }}
    />
  );
};

export default RecentSearches;
