import { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import CustomModal from '@/src/components/Modal';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  removeCommentOptimistic,
  fetchCommentReplies,
  addCommentOptimistic,
  removeScrapbookCommentOptimistic,
  fetchScrapbookCommentReplies,
  addScrapbookCommentOptimistic,
} from '@/src/redux/slices/content/contentSlice';
import { AppDispatch, RootState } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { BottomTabNavigationI } from '@/src/navigation/types';
import { deleteCommentAPI } from '@/src/networks/content/comment';
import { CommentItemI } from '@/src/networks/content/types';
import { deleteScrapbookComment } from '@/src/networks/port/scrapbook';
import CommentBody from '../CommentBody';
import { CommentItemProps, ReplyI } from './types';

const CommentItem = ({ item, postId, onReplyPress, type }: CommentItemProps) => {
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);
  const navigation = useNavigation<BottomTabNavigationI>();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDeleteReplyModal, setShowDeleteReplyModal] = useState(false);
  const [replyToDelete, setReplyToDelete] = useState<string | null>(null);

  const commentReplies = useSelector((state: RootState) => {
    if (type === 'SCRAPBOOK_POST') {
      return state.content.scrapbookCommentReplies[item.id];
    }
    return state.content.commentReplies[item.id];
  });

  const scrapbookComments = useSelector((state: RootState) => {
    if (type === 'SCRAPBOOK_POST') {
      return state.content.scrapbookComments[postId];
    }
    return null;
  });

  const isOwnComment = currentUser?.profileId === item.Profile?.id;

  const normalizeReplies = (replies: unknown[]) => {
    if (!Array.isArray(replies)) return [];
    return replies.map((reply) => {
      const replyObj = reply as Record<string, unknown> & { id: string };
      return {
        ...replyObj,
        createdAt:
          typeof replyObj.createdAt === 'string'
            ? replyObj.createdAt
            : (replyObj.createdAt as { toISOString?: () => string })?.toISOString?.() ||
              new Date().toISOString(),
        updatedAt:
          typeof replyObj.updatedAt === 'string'
            ? replyObj.updatedAt
            : (replyObj.updatedAt as { toISOString?: () => string })?.toISOString?.() ||
              new Date().toISOString(),
      };
    });
  };

  const getCurrentParentComment = () => {
    if (type === 'SCRAPBOOK_POST' && scrapbookComments?.comments) {
      return scrapbookComments.comments.find((c) => c.id === item.id);
    }
    return item;
  };

  const getAllReplies = () => {
    const currentParent = getCurrentParentComment();
    const initialReplies = normalizeReplies(currentParent?.replies || []);
    const loadedReplies = normalizeReplies(commentReplies?.comments || []);

    const repliesMap = new Map();

    initialReplies.forEach((reply) => {
      repliesMap.set(reply.id, reply);
    });

    loadedReplies.forEach((reply) => {
      repliesMap.set(reply.id, reply);
    });

    return Array.from(repliesMap.values()).sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    );
  };

  const allReplies = getAllReplies();
  const hasLoadedMoreReplies = commentReplies?.comments && commentReplies.comments.length > 0;

  const getDisplayReplies = () => {
    if (hasLoadedMoreReplies) {
      return allReplies;
    }
    return allReplies.slice(0, 2);
  };

  const displayReplies = getDisplayReplies();
  const totalRepliesCount = item.repliesCount || 0;
  const currentDisplayCount = displayReplies.length;

  const showViewMoreButton = totalRepliesCount > currentDisplayCount && !hasLoadedMoreReplies;
  const remainingCount = Math.max(0, totalRepliesCount - currentDisplayCount);

  const handleDeleteComment = async () => {
    setShowDeleteModal(false);

    const removeAction =
      type === 'SCRAPBOOK_POST'
        ? removeScrapbookCommentOptimistic({ scrapbookPostId: postId, commentId: item.id })
        : removeCommentOptimistic({ postId, commentId: item.id });

    dispatch(removeAction);

    try {
      if (type === 'SCRAPBOOK_POST') {
        await deleteScrapbookComment(item.id);
      } else {
        await deleteCommentAPI(item.id);
      }
    } catch (error) {
      const addAction =
        type === 'SCRAPBOOK_POST'
          ? addScrapbookCommentOptimistic({
              scrapbookPostId: postId,
              tempId: item.id,
              text: item.text,
              user: {
                profileId: item.Profile?.id!,
                fullName: item.Profile?.name!,
                avatar: item.Profile?.avatar || '',
                designation: item.Profile?.designation!,
                organisation: item.Profile?.entity!,
              },
            })
          : addCommentOptimistic({
              postId,
              tempId: item.id,
              text: item.text,
              user: {
                profileId: item.Profile?.id!,
                fullName: item.Profile?.name!,
                avatar: item.Profile?.avatar || '',
                designation: item.Profile?.designation!,
                organisation: item.Profile?.entity!,
              },
            });

      dispatch(addAction);

      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to delete comment',
      });
    }
  };

  const handleDeleteReply = async (replyId: string) => {
    setShowDeleteReplyModal(false);
    setReplyToDelete(null);

    const replyToDeleteObj = allReplies.find((r) => r.id === replyId);
    if (!replyToDeleteObj) return;

    const removeAction =
      type === 'SCRAPBOOK_POST'
        ? removeScrapbookCommentOptimistic({
            scrapbookPostId: postId,
            commentId: replyId,
            parentCommentId: item.id,
          })
        : removeCommentOptimistic({
            postId,
            commentId: replyId,
            parentCommentId: item.id,
          });

    dispatch(removeAction);

    try {
      if (type === 'SCRAPBOOK_POST') {
        await deleteScrapbookComment(replyId);
      } else {
        await deleteCommentAPI(replyId);
      }
    } catch (error) {
      const addAction =
        type === 'SCRAPBOOK_POST'
          ? addScrapbookCommentOptimistic({
              scrapbookPostId: postId,
              tempId: replyToDeleteObj.id,
              text: replyToDeleteObj.text,
              parentCommentId: item.id,
              user: {
                profileId: replyToDeleteObj.Profile?.id!,
                fullName: replyToDeleteObj.Profile?.name!,
                avatar: replyToDeleteObj.Profile?.avatar || '',
                designation: replyToDeleteObj.Profile?.designation || null,
                organisation: replyToDeleteObj.Profile?.entity || null,
              },
            })
          : addCommentOptimistic({
              postId,
              tempId: replyToDeleteObj.id,
              text: replyToDeleteObj.text,
              parentCommentId: item.id,
              user: {
                profileId: replyToDeleteObj.Profile?.id!,
                fullName: replyToDeleteObj.Profile?.name!,
                avatar: replyToDeleteObj.Profile?.avatar || '',
                designation: replyToDeleteObj.Profile?.designation || null,
                organisation: replyToDeleteObj.Profile?.entity || null,
              },
            });

      dispatch(addAction);

      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to delete reply',
      });
    }
  };

  const handleLoadMoreReplies = () => {
    const lastVisibleReply = displayReplies[displayReplies.length - 1];
    const cursorId = lastVisibleReply?.cursorId || lastVisibleReply?.id || null;

    const fetchParams = {
      parentCommentId: item.id,
      cursorId,
      pageSize: 10,
    };

    if (type === 'SCRAPBOOK_POST') {
      dispatch(
        fetchScrapbookCommentReplies({
          scrapbookPostId: postId,
          ...fetchParams,
        }),
      );
    } else {
      dispatch(
        fetchCommentReplies({
          postId,
          ...fetchParams,
        }),
      );
    }
  };

  const handleUserProfileNavigation = (profileId: string) => {
    if (profileId === currentUser.profileId) {
      const drawerNavigation = navigation.getParent()?.getParent();

      if (drawerNavigation) {
        drawerNavigation.reset({
          index: 0,
          routes: [
            {
              name: 'ProfileStack',
              params: {
                screen: 'UserProfile',
                params: { fromTabPress: false, profileId: currentUser.profileId },
              },
            },
          ],
        });
      }
    } else {
      navigation.navigate('HomeStack', {
        screen: 'OtherUserProfile',
        params: { fromTabPress: false, profileId: profileId },
      });
    }
  };

  const renderReply = (reply: Record<string, unknown> & ReplyI) => {
    const isOwnReply = currentUser?.profileId === reply.Profile?.id;

    return (
      <View key={reply.id} className="flex-row gap-3 mb-3">
        <Pressable onPress={() => handleUserProfileNavigation(reply.Profile?.id!)}>
          <UserAvatar avatarUri={reply.Profile?.avatar || null} />
        </Pressable>
        <View className="flex-1">
          <CommentBody
            item={reply as unknown as CommentItemI}
            isReply
            onUserPress={() => handleUserProfileNavigation(reply.Profile?.id!)}
          />
          {isOwnReply && (
            <View className="flex-row mt-2">
              <Pressable
                onPress={() => {
                  setReplyToDelete(reply.id);
                  setShowDeleteReplyModal(true);
                }}
              >
                <Text className="text-xs text-subLabelGray font-medium leading-4">Delete</Text>
              </Pressable>
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <View className="p-3">
      <View className="flex-row gap-3">
        <Pressable onPress={() => handleUserProfileNavigation(item.Profile.id)}>
          <UserAvatar avatarUri={item.Profile?.avatar} name={item.Profile.name} />
        </Pressable>
        <View className="flex-1">
          <CommentBody
            item={item}
            onUserPress={() => handleUserProfileNavigation(item.Profile.id)}
          />
          <View className="flex-row mt-2 gap-4">
            {isOwnComment && (
              <Pressable onPress={() => setShowDeleteModal(true)}>
                <Text className="text-xs text-subLabelGray font-medium leading-4">Delete</Text>
              </Pressable>
            )}
            <Pressable onPress={() => onReplyPress?.(item)}>
              <Text className="text-xs text-subLabelGray font-medium leading-4">Reply</Text>
            </Pressable>
          </View>
        </View>
      </View>

      {(displayReplies.length > 0 || showViewMoreButton) && (
        <View className="ml-12 mt-3">
          {displayReplies.map(renderReply)}

          {showViewMoreButton && (
            <Pressable onPress={handleLoadMoreReplies} className="mt-2 ml-4">
              <Text className="text-sm text-green-800 font-medium">
                View {remainingCount} more {remainingCount === 1 ? 'reply' : 'replies'}
              </Text>
            </Pressable>
          )}
        </View>
      )}
      <CustomModal
        isVisible={showDeleteModal}
        onCancel={() => setShowDeleteModal(false)}
        title="Delete Comment"
        description="Are you sure you want to delete this comment? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteComment}
        confirmButtonVariant="danger"
      />
      <CustomModal
        isVisible={showDeleteReplyModal}
        onCancel={() => {
          setShowDeleteReplyModal(false);
          setReplyToDelete(null);
        }}
        title="Delete Reply"
        description="Are you sure you want to delete this reply? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={() => replyToDelete && handleDeleteReply(replyToDelete)}
        confirmButtonVariant="danger"
      />
    </View>
  );
};

export default CommentItem;
