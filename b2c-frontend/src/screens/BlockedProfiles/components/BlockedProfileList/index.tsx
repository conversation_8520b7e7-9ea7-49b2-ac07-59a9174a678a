/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, ActivityIndicator, Pressable } from 'react-native';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import UsersList from '@/src/components/UsersList';
import { BottomTabNavigationI, RootDrawerParamListI } from '@/src/navigation/types';
import { BlockedProfilesListProps, BlockedUserI } from './types';
import { useBlockedProfiles } from './useHook';

const BlockedUserProfilesList: React.FC<BlockedProfilesListProps> = ({ onBack }) => {
  const {
    blockedUsers,
    loading,
    refreshing,
    isUnblocking,
    totalCount,
    handleRefresh,
    handleLoadMore,
    handleUnblock,
  } = useBlockedProfiles();

  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();

  const handleUserPress = (user: BlockedUserI) => {
    drawerNavigation.navigate('ProfileStack', {
      screen: 'UserProfile',
      params: { profileId: user.Profile.id, fromTabPress: false },
    });
  };

  const renderUnblockButton = (user: BlockedUserI) => {
    const profileId = user.Profile.id;
    const unblocking = isUnblocking[profileId] || false;

    return (
      <Pressable
        onPress={() => handleUnblock(profileId)}
        disabled={unblocking}
        className="bg-gray-100 rounded-full px-4 py-2 flex-row items-center justify-center"
      >
        {unblocking ? (
          <ActivityIndicator size="small" />
        ) : (
          <Text className="text-gray-800 font-medium">Unblock</Text>
        )}
      </Pressable>
    );
  };

  return (
    <>
      <View className="flex-row items-center justify-between px-4 py-2">
        <BackButton onBack={onBack} label="" />
        <Text className="text-gray-500">{totalCount} blocked</Text>
      </View>
      <UsersList
        data={blockedUsers}
        loading={loading}
        onLoadMore={handleLoadMore}
        onRefresh={handleRefresh}
        onPress={handleUserPress}
        refreshing={refreshing}
        renderActions={renderUnblockButton}
      />
    </>
  );
};

export default BlockedUserProfilesList;
