import { useCallback, useEffect, useState } from 'react';
import { PermissionsAndroid, Platform } from 'react-native';
import Config from 'react-native-config';
import Geolocation from 'react-native-geolocation-service';
import { useFieldArray, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectNearbyFilters } from '@/src/redux/selectors/announcement';
import {
  clearOtherLocation,
  setNearbyRadius,
  setOtherLocation,
  setSelfLocation,
} from '@/src/redux/slices/announcement/announcementSlice';
import { AppDispatch } from '@/src/redux/store';
import { debounceAsync } from '@/src/utilities/search/debounce';
import { showToast } from '@/src/utilities/toast';
import { NearbySettingsFormFiltersI } from './types';

export const useNearbySettings = () => {
  const isOtherLocationAdded = Boolean(useSelector(selectNearbyFilters).otherLocation.length);
  const dispatch = useDispatch<AppDispatch>();
  const [selfLocationSuggestions, setSelfLocationSuggestions] = useState<any[]>([]);
  const [otherLocationSuggestions, setOtherLocationSuggestions] = useState<any[]>([]);

  const [loading, setLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);

  // Mapbox Geocoding API call
  const fetchLocationSuggestions = async (query: string) => {
    try {
      setIsFetching(true);
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(query)}.json?access_token=${Config.MAPBOX_ACCESS_TOKEN}`,
      );
      const data = await response.json();
      return data.features || [];
    } catch (error) {
      console.error('Geocoding error:', error);
      return [];
    } finally {
      setIsFetching(false);
    }
  };

  const debouncedSelfLocationSearch = useCallback(
    debounceAsync(async (query) => {
      if (query.length > 2) {
        const results = await fetchLocationSuggestions(query);
        setSelfLocationSuggestions(results);
      } else {
        setSelfLocationSuggestions([]);
      }
    }, 400),
    [],
  );

  const debouncedOtherLocationSearch = useCallback(
    debounceAsync(async (query) => {
      if (query.length > 2) {
        const results = await fetchLocationSuggestions(query);
        setOtherLocationSuggestions(results);
      } else {
        setOtherLocationSuggestions([]);
      }
    }, 400),
    [],
  );

  const methods = useForm<NearbySettingsFormFiltersI>({
    mode: 'onChange',
    defaultValues: {
      selfLocation: '',
      otherLocation: '',
      radius: useSelector(selectNearbyFilters).radius || 0,
    },
  });

  useEffect(() => {
    const requestLocationPermission = async () => {
      if (Platform.OS === 'android') {
        try {
          setLoading(true);
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            {
              title: 'Location Permission',
              message: 'This app needs access to your location',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            },
          );
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            getCurrentLocation();
          }
        } catch (err) {
          showToast({
            type: 'error',
            message: 'Error.Try after sometime',
          });
        } finally {
          setLoading(false);
        }
      } else {
        getCurrentLocation();
      }
    };

    const getCurrentLocation = () => {
      Geolocation.getCurrentPosition(
        async (position) => {
          try {
            const { longitude, latitude } = position.coords;

            dispatch(setSelfLocation([longitude, latitude]));

            const response = await fetch(
              `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${Config.MAPBOX_ACCESS_TOKEN}`,
            );

            const data = await response.json();

            // Find the first address result
            const addressItem = data.features?.find((item) => item.id.startsWith('address.'));

            if (addressItem) {
              methods.setValue('selfLocation', addressItem.place_name, { shouldDirty: true });
            } else {
              const fallbackName = data.features[0]?.place_name || 'Current Location';
              methods.setValue('selfLocation', fallbackName, { shouldDirty: true });
            }
          } catch (error) {
            console.error('Location fetch error:', error);
            showToast({
              type: 'error',
              message: 'Failed to get location details',
            });
          }
        },
        (err) => {
          showToast({
            type: 'error',
            message: err.message,
          });
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
      );
    };

    requestLocationPermission();
  }, []);

  const handleAddOtherLocation = () => {
    dispatch(setOtherLocation([0, 0]));
  };

  const handleDeleteOtherLocation = () => {
    dispatch(clearOtherLocation());
  };

  const handleSelfLocationChange = (text: string) => {
    methods.setValue('selfLocation', text, { shouldDirty: true });
    debouncedSelfLocationSearch(text);
  };

  const handleOtherLocationChange = (text: string) => {
    methods.setValue('otherLocation', text, { shouldDirty: true });
    debouncedOtherLocationSearch(text);
  };

  const handleSelectSuggestion = (suggestion: any, fieldName: 'selfLocation' | 'otherLocation') => {
    const [longitude, latitude] = suggestion.center;
    methods.setValue(fieldName, suggestion.place_name);

    if (fieldName === 'selfLocation') {
      setSelfLocationSuggestions([]);
      dispatch(setSelfLocation([longitude, latitude]));
    } else {
      setOtherLocationSuggestions([]);
      dispatch(setOtherLocation([longitude, latitude]));
    }
  };

  const hasChanges = methods.formState.isDirty;

  const radius = methods.watch('radius');
  const onSubmit = async (data) => {
    dispatch(setNearbyRadius(Number(radius)));
  };

  return {
    methods,
    hasChanges,
    handleAddOtherLocation,
    isOtherLocationAdded,
    handleDeleteOtherLocation,
    loading,
    handleSelfLocationChange,
    handleOtherLocationChange,
    selfLocationSuggestions,
    otherLocationSuggestions,
    isFetching,
    handleSelectSuggestion,
    setSelfLocationSuggestions,
    setOtherLocationSuggestions,
    onSubmit,
  };
};
