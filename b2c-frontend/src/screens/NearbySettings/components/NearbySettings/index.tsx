import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import TextInput from '@/src/components/TextInput';
import { selectNearbyFilters } from '@/src/redux/selectors/announcement';
import AddItem from '@/src/assets/svgs/AddItem';
import TrashBin from '@/src/assets/svgs/TrashBin';
import InformationText from '../InformationText';
import { EditNearbySettingsPropsI } from './types';
import { useNearbySettings } from './useHook';

const EditNearbySettings = ({ onBack }: EditNearbySettingsPropsI) => {
  const {
    hasChanges,
    methods,
    handleAddOtherLocation,
    isOtherLocationAdded,
    handleDeleteOtherLocation,
    loading,
    handleOtherLocationChange,
    handleSelfLocationChange,
    selfLocationSuggestions,
    otherLocationSuggestions,
    isFetching,
    handleSelectSuggestion,
    setSelfLocationSuggestions,
    setOtherLocationSuggestions,
    onSubmit,
  } = useNearbySettings();

  const radius = useSelector(selectNearbyFilters).radius;

  const { control, handleSubmit } = methods;

  if (loading) {
    return <ActivityIndicator />;
  }

  return (
    <ScrollView className="flex-1 bg-white px-4" showsVerticalScrollIndicator={false}>
      <View className="flex-row items-center justify-between py-4">
        <BackButton onBack={onBack} label="Nearby Settings" />
        <Pressable onPress={handleSubmit(onSubmit)} disabled={!hasChanges}>
          <Text
            className={`text-lg font-medium ${!hasChanges ? 'text-gray-400' : 'text-[#448600]'}`}
          >
            Apply
          </Text>
        </Pressable>
      </View>
      <View className="gap-4">
        <InformationText />
        <Controller
          control={control}
          name="selfLocation"
          rules={{ required: 'Location is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <View>
              <TextInput
                label="Location"
                value={value}
                onChangeText={(text) => {
                  onChange(text);
                  handleSelfLocationChange(text);
                }}
              />
              {isFetching && <ActivityIndicator size="small" />}
              {selfLocationSuggestions.length > 0 && (
                <View className="mt-2 border border-gray-200 rounded-lg">
                  {selfLocationSuggestions.map((suggestion) => (
                    <Pressable
                      key={suggestion.id}
                      className="p-3 border-b border-gray-100"
                      onPress={() => {
                        handleSelectSuggestion(suggestion, 'selfLocation');
                        setSelfLocationSuggestions([]);
                      }}
                    >
                      <Text>{suggestion.place_name}</Text>
                    </Pressable>
                  ))}
                </View>
              )}
              {error && <Text className="text-red-500">{error.message}</Text>}
            </View>
          )}
        />

        <Pressable onPress={handleAddOtherLocation}>
          <AddItem color={isOtherLocationAdded ? 'gray' : '#448600'} />
        </Pressable>

        {isOtherLocationAdded && (
          <View>
            <Controller
              control={control}
              name="otherLocation"
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <View>
                  <TextInput
                    label="Other Location"
                    value={value}
                    onChangeText={(text) => {
                      onChange(text);
                      handleOtherLocationChange(text);
                    }}
                  />
                  {isFetching && <ActivityIndicator size="small" />}
                  {otherLocationSuggestions.length > 0 && (
                    <View className="mt-2 border border-gray-200 rounded-lg">
                      {otherLocationSuggestions.map((suggestion) => (
                        <Pressable
                          key={suggestion.id}
                          className="p-3 border-b border-gray-100"
                          onPress={() => {
                            handleSelectSuggestion(suggestion, 'otherLocation');
                            setOtherLocationSuggestions([]);
                          }}
                        >
                          <Text>{suggestion.place_name}</Text>
                        </Pressable>
                      ))}
                    </View>
                  )}
                </View>
              )}
            />
            <View className="flex-row justify-between">
              <View></View>
              <Pressable className="m-3" onPress={handleDeleteOtherLocation}>
                <TrashBin width={2.5} height={2.5} color="red" />
              </Pressable>
            </View>
          </View>
        )}

        <Controller
          control={control}
          name="radius"
          rules={{ required: 'Radius is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <TextInput
              label="Radius(km)"
              value={value?.toString()} // Convert to string for display
              onChangeText={(text) => {
                // Convert to number when saving
                const numValue = text ? parseFloat(text) : 0;
                onChange(numValue);
              }}
              keyboardType="numeric"
              error={error?.message}
            />
          )}
        />
      </View>
    </ScrollView>
  );
};

export default EditNearbySettings;
