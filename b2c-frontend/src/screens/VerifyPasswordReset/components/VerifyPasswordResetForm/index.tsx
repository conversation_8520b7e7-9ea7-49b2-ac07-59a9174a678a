import { useEffect, useRef, useState } from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  TouchableWithoutFeedback,
  View,
  Text,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import OTPInput from '@/src/components/OtpInput';
import { OTPInputHandle } from '@/src/components/OtpInput/types';
import { AppStackParamListI } from '@/src/navigation/types';
import { VerifyPasswordResetFormProps } from './types';
import { useVerifyPasswordReset } from './useHook';

const RESEND_OTP_COUNTDOWN = 90;

export function VerifyPasswordResetForm({ email, onBack }: VerifyPasswordResetFormProps) {
  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();

  const { isSubmitting, otp, setOtp, handleVerify, handleResend } = useVerifyPasswordReset(
    email,
    () => {
      navigation.navigate('ResetPassword', { email });
    },
  );

  const otpRef = useRef<OTPInputHandle>(null);
  const [countdown, setCountdown] = useState(RESEND_OTP_COUNTDOWN);
  const [isResending, setIsResending] = useState(false);

  useEffect(() => {
    if (countdown <= 0) return;
    const timer = setInterval(() => setCountdown((prev) => prev - 1), 1000);
    return () => clearInterval(timer);
  }, [countdown]);

  const handleResendOTP = async () => {
    if (countdown > 0 || isResending) return;

    try {
      setIsResending(true);
      await handleResend();

      setCountdown(RESEND_OTP_COUNTDOWN);
      setOtp('');
      otpRef.current?.clear();
    } catch (error) {
      console.error('Failed to resend OTP:', error);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <View className="ml-2">
          <BackButton onBack={onBack} />
        </View>
        <View className="px-5 gap-2">
          <OTPInput
            title="Forgot Password"
            subtitle={`Please enter the code sent to ${email}`}
            value={otp}
            onChange={setOtp}
            autoFocus
            ref={otpRef}
          />
          <View className="mt-8">
            <Button
              label="Continue"
              onPress={handleVerify}
              variant={otp.length === 6 ? 'primary' : 'tertiary'}
              disabled={otp.length !== 6 || isSubmitting}
              loading={isSubmitting}
            />
            <View className="flex-row justify-center mt-4 gap-1">
              <Text className="text-gray-500">Didn't get the code?</Text>
              <Pressable
                onPress={handleResendOTP}
                disabled={countdown > 0 || isResending || isSubmitting}
              >
                <Text className={countdown > 0 || isResending ? 'text-gray-400' : 'text-green-700'}>
                  {isResending
                    ? 'Sending...'
                    : countdown > 0
                      ? `Resend in ${countdown}s`
                      : 'Resend Code'}
                </Text>
              </Pressable>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
}
