import { MapboxContext } from '@/src/redux/slices/announcement/types';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export type EditAnnouncementPropsI = {
  onBack: () => void;
  profileId: string;
  announcementId: string;
};

export type AnnouncementDetailsFormDataI = {
  title: string;
  message: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
};

export type LocationData = {
  center: number[];
  context: MapboxContext[];
  id: string;
  geometry: {
    type: string;
    coordinates: number[];
  };
  place_name: string;
  place_type: string[];
  properties: {
    accuracy: string;
    mapbox_id: string;
  };
  relevance: number;
  text: string;
  type: string;
};

export type postCodePlaceDetail = {
  id: string;
  text: string;
};

export type CityCountryPostCodeDetails = {
  postcode?: postCodePlaceDetail;
  place?: postCodePlaceDetail;
  country?: {
    id: string;
    short_code: string;
  };
};

export type portCoordinatesSearchResultI = {
  city: SearchResultI;
  dataType: string;
  latitude: string;
  longitude: string;
  name: string;
  unLocode: string;
};
