import { useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import EntitySearch from '@/src/components/EntitySearch';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { clearLocationData } from '@/src/redux/slices/announcement/announcementSlice';
import type { AppDispatch } from '@/src/redux/store';

const Port = () => {
  const navigation = useNavigation();
  const portSelection = useSelector(selectSelectionByKey('port-coordinates'));
  const dispatch = useDispatch<AppDispatch>();
  console.log('po=', portSelection);

  useEffect(() => {
    if (portSelection) {
      dispatch(clearLocationData());
      // navigation.goBack();
    }
  }, [portSelection]);

  return (
    <EntitySearch
      title=""
      placeholder="Enter Port"
      selectionKey="port-coordinates"
      data={portSelection?.name}
      titleClassName="hidden"
      className="px-3"
    />
  );
};

export default Port;
