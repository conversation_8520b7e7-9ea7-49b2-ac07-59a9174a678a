import { Text, View } from 'react-native';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation, useRoute } from '@react-navigation/native';
import Button from '@/src/components/Button';
import NotFound from '@/src/components/NotFound';
import type { PortI } from '@/src/redux/slices/experience/types';
import type { BottomTabNavigationI, RootDrawerParamListI } from '@/src/navigation/types';

const PortsVisited = ({
  ports,
  total,
  profileId,
}: {
  ports: PortI[];
  total: number;
  profileId: string;
}) => {
  const showMore = total > 5;
  const bottomTabNavigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const route = useRoute();

  const handleShowMore = () => {
    if (route.name === 'UserProfile') {
      drawerNavigation.navigate('ProfileStack', {
        screen: 'PortsVisited',
        params: { profileId: profileId },
      });
    } else {
      bottomTabNavigation.navigate('HomeStack', {
        screen: 'PortsVisited',
        params: { profileId: profileId },
      });
    }
  };

  return ports.length !== 0 ? (
    <>
      <View>
        <View className="flex-row mb-4 text-tableHeaderBlack mt-7">
          <View className="flex-1">
            <Text className="font-medium text-sm">Port Name</Text>
          </View>
          <View className="flex-1">
            <Text className="font-medium text-sm">Country</Text>
          </View>
        </View>
        <View className="border-t border-borderGrayExtraLight">
          {ports.map((port, index) => (
            <View
              key={index}
              className={`flex-row py-4 ${index < ports.length - 1 ? 'border-b border-borderGrayExtraLight' : ''}`}
            >
              <View className="flex-1">
                <Text className="text-sm leading-4 text-tableHeaderBlack">{port.name}</Text>
              </View>
              <View className="flex-1">
                <Text className="text-tableHeaderBlack">{port.country.name}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
      {showMore && (
        <View className="my-4">
          <Button variant="outline" label="See More" onPress={handleShowMore} />
        </View>
      )}
    </>
  ) : (
    <NotFound fullScreen={false} imageStyle={{ width: 120, height: 120 }} />
  );
};

export default PortsVisited;
