import { useEffect, useState } from 'react';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import { showToast } from '@/src/utilities/toast';
import type { RootDrawerParamListI } from '@/src/navigation/types';
import { apiCall } from '@/src/services/api';
import type {
  ExperienceFetchParamsI,
  ExperienceFetchReponseI,
} from '@/src/networks/experience/types';

const useProfileExperience = (profileId: string) => {
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const [experiences, setExperiences] = useState<ExperienceFetchReponseI>({
    data: [],
    total: 0,
    portVisits: {
      data: [],
      total: 0,
    },
  });

  useEffect(() => {
    fetchExperiences();
  }, [profileId]);

  const fetchExperiences = async () => {
    if (loading) return;
    setLoading(true);
    try {
      const respone = await apiCall<ExperienceFetchParamsI, ExperienceFetchReponseI>(
        '/backend/api/v1/career/profile-experiences',
        'GET',
        {
          isAuth: true,
          query: { profileId, page: 0, pageSize: 3 },
        },
      );
      setExperiences(respone);
    } catch (error) {
      showToast({
        message: 'Failed to fetch experiences',
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('ProfileStack', {
      screen: 'EditExperienceList',
      params: {
        profileId,
        editable: true,
      },
    });
  };

  return {
    loading,
    experiences,
    fetchExperiences,
    handleEdit,
  };
};

export default useProfileExperience;
