import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import {
  selectAboutProfileCertificationsStatutoryCount,
  selectAboutProfileStatutoryCertifications,
} from '@/src/redux/selectors/about';
import { BottomTabNavigationI, RootDrawerParamListI } from '@/src/navigation/types';

export const useProfileStatutory = (profileId: string, isUserProfile: boolean) => {
  const certifications = useSelector(selectAboutProfileStatutoryCertifications);
  const count = useSelector(selectAboutProfileCertificationsStatutoryCount);
  const bottomTabNavigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();

  const onListCertifications = () => {
    if (isUserProfile) {
      drawerNavigation.navigate('ProfileStack', {
        screen: 'EditCertificationList',
        params: {
          editable: false,
          tab: 'statutory',
          profileId: profileId,
        },
      });
    } else {
      bottomTabNavigation.navigate('HomeStack', {
        screen: 'EditCertificationList',
        params: {
          editable: false,
          tab: 'statutory',
          profileId: profileId,
        },
      });
    }
  };

  return {
    certifications,
    count,
    onListCertifications,
  };
};
