import { Text, TouchableOpacity, View } from 'react-native';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import NotFound from '@/src/components/NotFound';
import { BottomTabNavigationI, RootDrawerParamListI } from '@/src/navigation/types';
import { IdentityDocumentDetails } from '../DocumentDetails';
import { expiresIn } from '../utils';
import { IdentityDocumentI, ProfileIdentityPropsI } from './types';
import { useProfileIdentity } from './useHook';

const ProfileIdentity: React.FC<ProfileIdentityPropsI> = ({ profileId, isUserProfile }) => {
  const { documents, count } = useProfileIdentity(profileId);
  const bottomTabNavigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  return (
    <View>
      {documents.map((item: IdentityDocumentI, index: number) => {
        return (
          <IdentityDocumentDetails
            key={index}
            document={item}
            isLast={index === documents.length - 1}
            expiresIn={expiresIn(item.untilDate)}
          />
        );
      })}
      {count > 3 && (
        <TouchableOpacity
          onPress={() => {
            if (isUserProfile) {
              drawerNavigation.navigate('ProfileStack', {
                screen: 'EditDocumentList',
                params: { editable: false, tab: 'identity', profileId: profileId },
              });
            } else {
              bottomTabNavigation.navigate('HomeStack', {
                screen: 'EditDocumentList',
                params: { editable: false, tab: 'identity', profileId: profileId },
              });
            }
          }}
        >
          <Text className="text-[#448600] text-base font-medium pt-4">{`View all ${count} identity documents`}</Text>
        </TouchableOpacity>
      )}
      {count === 0 && (
        <NotFound
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default ProfileIdentity;
