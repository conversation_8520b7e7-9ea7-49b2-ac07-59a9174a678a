import { Text, TouchableOpacity, View } from 'react-native';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import NotFound from '@/src/components/NotFound';
import { SectionHeader } from '@/src/components/SectionHeader';
import TextView from '@/src/components/TextView';
import type { BottomTabNavigationI, RootDrawerParamListI } from '@/src/navigation/types';
import EditPencil from '@/src/assets/svgs/EditPencil';
import EducationIcon from '@/src/assets/svgs/Education';
import type { EducationI, ProfileEducationPropsI } from './types';
import { useProfileEducation } from './useHook';

const RenderEachEducation = ({ item, isLast }: { item: EducationI; isLast: boolean }) => {
  return (
    <View className={`${isLast ? '' : 'border-b border-[#D4D4D4]'} py-5`}>
      <TextView
        subtitle={item.entity.name}
        subtitleClassName="font-medium text-base leading-5 text-[#000000]"
      />
      <TextView
        subtitle={item.degree.name}
        subtitleClassName="text-sm leading-5 mt-2 text-[#000000]"
      />
      <View className="mt-2">
        <TextView
          subtitle={`${new Date(item.fromDate).toLocaleDateString('en-US', {
            month: 'long',
            year: 'numeric',
          })} - ${
            item.toDate
              ? new Date(item.toDate).toLocaleDateString('en-US', {
                  month: 'long',
                  year: 'numeric',
                })
              : 'Present'
          }`}
          subtitleClassName="text-sm text-[#737373]"
        />
      </View>
    </View>
  );
};

const ProfileEducation = ({ isUserProfile, profileId }: ProfileEducationPropsI) => {
  const { educations, count } = useProfileEducation();
  const bottomTabNavigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();

  return (
    <View className="mt-4">
      <View className="flex-row items-center justify-between">
        <SectionHeader title="Education" icon={EducationIcon} />
        {isUserProfile && (
          <TouchableOpacity
            onPress={() =>
              drawerNavigation.navigate('ProfileStack', {
                screen: 'EditEducationList',
                params: { editable: true },
              })
            }
          >
            <EditPencil width={2.3} height={2.3} />
          </TouchableOpacity>
        )}
      </View>
      {count === 0 ? (
        <NotFound
          className="py-6"
          titleClassName="font-normal text-base text-[#4B5563]"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      ) : (
        (educations.length > 3 ? educations.slice(0, 3) : educations).map((item, index) => (
          <RenderEachEducation
            item={item}
            key={index}
            isLast={educations.length > 3 ? index === 2 : index === educations.length - 1}
          />
        ))
      )}
      {count > 3 && (
        <TouchableOpacity
          onPress={() =>
            isUserProfile
              ? drawerNavigation.navigate('ProfileStack', {
                  screen: 'EditEducationList',
                  params: { editable: false, profileId: profileId },
                })
              : bottomTabNavigation.navigate('HomeStack', {
                  screen: 'EditEducationList',
                  params: { editable: false, profileId: profileId },
                })
          }
        >
          <Text className="text-[#448600] text-base font-medium pt-4">{`View all ${count} Education Experiences`}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ProfileEducation;
