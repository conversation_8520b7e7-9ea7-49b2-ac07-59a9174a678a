import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { HomeStackParamListI } from '@/src/navigation/types';
import PeopleAttending from './components/PeopleAttending';

type RouteProps = RouteProp<HomeStackParamListI, 'PeopleAttending'>;

const PeopleAttendingScreen = () => {
  const route = useRoute<RouteProps>();
  const { annoucementId } = route.params || {};
  const navigation = useNavigation();

  return (
    <SafeArea>
      <PeopleAttending onBack={navigation.goBack} annoucementId={annoucementId} />
    </SafeArea>
  );
};

export default PeopleAttendingScreen;
