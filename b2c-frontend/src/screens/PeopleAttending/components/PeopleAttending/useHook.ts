import { useEffect, useState, useCallback } from 'react';
import { showToast } from '@/src/utilities/toast';
import { sendConnectionRequestAPI } from '@/src/networks/connect/connection';
import { fetchPeopleAttendingAPI } from '@/src/networks/nearby/announcement';
import { fetchPeopleAttendingResultDataI } from '@/src/networks/nearby/types';
import { RequestStatusTypeE } from '@/src/networks/port/types';

export const usePeopleAttending = (annoucementId: string) => {
  const [people, setPeople] = useState<fetchPeopleAttendingResultDataI[]>([]);
  const [cursorId, setCursorId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchPeople = useCallback(
    async (loadMore = false) => {
      try {
        if (loadMore) {
          if (!cursorId || !hasMore) return;
          setLoadingMore(true);
        } else {
          setLoading(true);
        }

        const query = {
          id: annoucementId,
          cursorId: loadMore ? Number(cursorId) : null,
          pageSize: 10,
        };

        const result = await fetchPeopleAttendingAPI(query);

        if (loadMore) {
          setPeople((prev) => [...prev, ...result.data]);
        } else {
          setPeople(result.data);
        }

        setCursorId(result.nextCursorId);
        setHasMore(result.nextCursorId !== null);
      } catch (error) {
        showToast({
          type: 'error',
          message: 'Failed to load attendees',
        });
      } finally {
        if (loadMore) {
          setLoadingMore(false);
        } else {
          setLoading(false);
        }
      }
    },
    [annoucementId, cursorId, hasMore],
  );

  useEffect(() => {
    fetchPeople();
  }, [fetchPeople]);

  const loadMorePeople = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchPeople(true);
    }
  }, [loadingMore, hasMore, fetchPeople]);

  const addConnection = async (receiverProfileId: string) => {
    try {
      const payload = {
        receiverProfileId,
        requestedStatus: 'PENDING' as RequestStatusTypeE,
      };
      await sendConnectionRequestAPI(payload);
      showToast({
        type: 'success',
        message: 'Connection request sent',
      });
    } catch (e) {
      showToast({
        type: 'error',
        message: 'Error sending request. Try Again',
      });
    }
  };

  const refreshPeople = () => {
    try {
      setIsRefreshing(true);
      fetchPeople();
    } catch (e) {
      showToast({
        type: 'error',
        message: 'Refresh failed.Try Again',
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  return {
    people,
    addConnection,
    loading,
    loadingMore,
    hasMore,
    loadMorePeople,
    isRefreshing,
    refreshPeople,
  };
};
