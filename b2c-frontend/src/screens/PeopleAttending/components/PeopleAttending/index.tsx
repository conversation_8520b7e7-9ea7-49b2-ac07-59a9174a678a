import { ActivityIndicator, Pressable } from 'react-native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import AddConnection from '@/src/assets/svgs/AddConnection';
import Tick from '@/src/assets/svgs/Tick';
import { PeopleAttendingPropsI } from './types';
import { usePeopleAttending } from './useHook';

const PeopleAttending = ({ onBack, annoucementId }: PeopleAttendingPropsI) => {
  const { people, addConnection, loadMorePeople, loading, loadingMore, hasMore, refreshPeople } =
    usePeopleAttending(annoucementId);

  const renderActions = (item: ListItem) => {
    if (item.status === 'NOT_CONNECTED') {
      return (
        <Pressable
          className="border border-[#448600] p-1 rounded-lg"
          onPress={() => addConnection(item.Profile.id)}
        >
          <AddConnection />
        </Pressable>
      );
    }
  };

  const renderFooter = () => {
    if (!loadingMore) return null;
    return <ActivityIndicator size="small" className="my-4" />;
  };

  return (
    <SafeArea>
      <BackButton onBack={onBack} label="People attending this event" />
      <UsersList
        data={people as unknown as ListItem[]}
        renderActions={renderActions}
        onLoadMore={loadMorePeople}
        loading={loading}
        onRefresh={refreshPeople}
      />
    </SafeArea>
  );
};

export default PeopleAttending;
