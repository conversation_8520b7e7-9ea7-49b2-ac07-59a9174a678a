/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { Announcement } from '@/src/components/Announcement';
import { navigate } from '@/src/utilities/navigation';
import AddItem from '@/src/assets/svgs/AddItem';
import AnnouncementIcon from '@/src/assets/svgs/Announcement';
import { useAnnouncements } from './useHook';

export const Announcements = () => {
  const {
    selfProfileId,
    announcements,
    loading,
    isLoadingMore,
    hasMore,
    fetchAnnoucements,
    loadMoreAnnouncements,
    deleteAnnouncement,
  } = useAnnouncements();

  if (loading) {
    return <ActivityIndicator />;
  }

  const renderFooter = () => {
    return (
      <View className="pb-4">
        {isLoadingMore && <ActivityIndicator size="small" className="my-4" />}
      </View>
    );
  };

  return (
    <View className="flex-1 px-4">
      <FlatList
        showsVerticalScrollIndicator={false}
        data={announcements}
        keyExtractor={(item) => item.announcementId}
        renderItem={({ item }) => (
          <Announcement
            announcement={item}
            user={item.profile}
            selfProfileId={selfProfileId}
            onDelete={deleteAnnouncement}
          />
        )}
        ListHeaderComponent={
          <View className="flex-row justify-between items-center py-2">
            <Text className="text-lg font-medium">{`All (${announcements.length})`}</Text>
            <Pressable className="p-2">{/* <AnnouncementIcon /> */}</Pressable>
          </View>
        }
        onEndReached={loadMoreAnnouncements}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        refreshing={loading}
        onRefresh={() => fetchAnnoucements(false)}
        contentContainerStyle={{ paddingBottom: 60 }}
      />
      <Pressable
        className="absolute right-6 bottom-20 bg-[#DDEFC8] w-14 h-14 rounded-full justify-center items-center shadow-lg z-10"
        onPress={() => navigate('EditAnnouncement')}
      >
        <AddItem width={4} height={4} stroke="#DDEFC8" />
      </Pressable>
    </View>
  );
};
