import { useState, useMemo, useCallback, useEffect } from 'react';
import { Platform, PermissionsAndroid } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Geolocation from 'react-native-geolocation-service';
import { useSelector, useDispatch } from 'react-redux';
import { selectNearbyFilters } from '@/src/redux/selectors/announcement';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { setSelfLocation } from '@/src/redux/slices/announcement/announcementSlice';
import { AppDispatch } from '@/src/redux/store';
import { formatLongitude, formatLatitude } from '@/src/utilities/location/coordinates';
import { fetchAnnoucementsAPI } from '@/src/networks/nearby/announcement';
import { fetchAnnoucementI } from '@/src/networks/nearby/types';

export const useAnnouncements = () => {
  const [announcements, setAnnouncements] = useState<fetchAnnoucementI[]>([]);
  const filters = useSelector(selectNearbyFilters);
  const dispatch = useDispatch<AppDispatch>();
  const selfProfileId = useSelector(selectCurrentUser).profileId;
  const [loading, setLoading] = useState(false);
  const [cursorId, setCursorId] = useState<number | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const coordinates = useMemo(() => {
    const coords = [];

    if (filters.selfLocation?.length === 2) {
      coords.push({
        longitude: formatLongitude(filters.selfLocation[0]),
        latitude: formatLatitude(filters.selfLocation[1]),
      });
    } else {
      coords.push({ longitude: 0, latitude: 0 });
    }

    if (filters.otherLocation.length === 2) {
      coords.push({
        longitude: formatLongitude(filters.otherLocation[0]),
        latitude: formatLatitude(filters.otherLocation[1]),
      });
    } else {
      coords.push({ longitude: 0, latitude: 0 });
    }

    return coords;
  }, [filters.selfLocation, filters.otherLocation]); // Only recompute when these change

  const requestLocationPermission = useCallback(async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'This app needs access to your location',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          return true;
        }
        return false;
      } catch (err) {
        console.log('Error requesting location permission');
        return false;
      }
    } else if (Platform.OS === 'ios') {
      const status = await Geolocation.requestAuthorization('whenInUse');
      return status === 'granted';
    }
    return true;
  }, []);

  const getCurrentLocation = useCallback(() => {
    return new Promise<void>((resolve) => {
      Geolocation.getCurrentPosition(
        (position) => {
          dispatch(setSelfLocation([position.coords.longitude, position.coords.latitude]));
          resolve();
        },
        (err) => {
          console.log('error getting location ', err.message);
          resolve();
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
      );
    });
  }, [dispatch]);

  const fetchAnnoucements = useCallback(
    async (loadMore = false) => {
      try {
        if (loadMore) {
          if (!cursorId || !hasMore) return;
          setIsLoadingMore(true);
        } else {
          setLoading(true);
        }

        // Only request location if we don't have selfLocation
        if (!filters.selfLocation || filters.selfLocation.length !== 2) {
          const hasPermission = await requestLocationPermission();
          if (hasPermission) {
            await getCurrentLocation();
          }
        }

        const payload = {
          coordinates,
          radius: 40,
          cursorId: loadMore ? cursorId : null,
          pageSize: 10,
        };

        const result = await fetchAnnoucementsAPI(payload);

        if (loadMore) {
          setAnnouncements((prev) => [...prev, ...result.data]);
        } else {
          setAnnouncements(result.data);
        }

        setCursorId(result.nextCursorId);
        setHasMore(result.nextCursorId !== null);
      } catch (error) {
        console.log('error loading announcement', error);
      } finally {
        if (loadMore) {
          setIsLoadingMore(false);
        } else {
          setLoading(false);
        }
      }
    },
    [
      coordinates,
      cursorId,
      filters.selfLocation,
      getCurrentLocation,
      hasMore,
      requestLocationPermission,
    ],
  );

  useFocusEffect(
    useCallback(() => {
      fetchAnnoucements();
    }, [fetchAnnoucements]),
  );

  const loadMoreAnnouncements = useCallback(() => {
    if (!cursorId || !hasMore || isLoadingMore) return;
    fetchAnnoucements(true);
  }, [cursorId, hasMore, isLoadingMore, fetchAnnoucements]);

  const deleteAnnouncement = useCallback((announcementId: string) => {
    setAnnouncements((prev) => prev.filter((ann) => ann.announcementId !== announcementId));
  }, []);

  return {
    announcements,
    selfProfileId,
    loading,
    isLoadingMore,
    hasMore,
    loadMoreAnnouncements,
    fetchAnnoucements,
    deleteAnnouncement,
  };
};
