import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { updateCommunity } from '@/src/redux/slices/community/communitySlice';
import { RootState } from '@/src/redux/store';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CreateCommunityHeader from '../CreateCommunity/components/CreateCommunityHeader';
import CommunityRestrictionForm from './components/CommunityRestrictionForm';

const CommunityRestrictionScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch();
  const { access, isRestricted } = useSelector((state: RootState) => state.community);

  const handleNext = () => {
    dispatch(updateCommunity({ access, isRestricted }));
    // navigation.navigate('People');
  };
  return (
    <SafeArea>
      <View className="flex-1 px-5">
        <CreateCommunityHeader currentPage={2} onNext={() => handleNext()} />
        <CommunityRestrictionForm />
      </View>
    </SafeArea>
  );
};

export default CommunityRestrictionScreen;
