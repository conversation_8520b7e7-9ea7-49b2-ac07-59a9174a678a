import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch } from 'react-redux';
import {
  addShipExperience,
  removeClientEquipment,
} from '@/src/redux/slices/experience/experienceSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import { ShipCreateEditPayloadI, ShipEquipmentI } from '../EditShipItem/types';

export const useEquipment = (
  equipments: ShipEquipmentI[],
  refetch: () => void,
  shipId?: string,
  shipTempId?: string,
) => {
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const [shipEquipments, setShipEquipments] = useState<ShipEquipmentI[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [deleteEquipmentId, setDeleteEquimentId] = useState<string | null>();

  useEffect(() => {
    setLoading(true);
    if (equipments?.length > 0) {
      setShipEquipments(equipments);
    } else {
      setShipEquipments([]);
    }
    setLoading(false);
  }, [equipments]);

  const handleAddEditEquipment = (payload?: ShipCreateEditPayloadI[], equipmentId?: string) => {
    if (payload) {
      navigation.navigate('EditEquipmentItem', {
        profileId: 'string',
        experienceId: 'string',
        shipId: shipId || 'string',
        equipmentId: equipmentId,
        data: payload,
        refetch: refetch,
        isClientMode: false,
      });
    }
  };

  const handleClientAdd = () => {
    navigation.navigate('EditEquipmentItem', {
      profileId: '',
      experienceId: '',
      shipId: undefined,
      equipmentId: undefined,
      data: [],
      refetch: () => {},
      isClientMode: true,
      shipTempId: shipTempId,
    });
  };

  const handleClientEdit = (tempId: string) => {
    navigation.navigate('EditEquipmentItem', {
      profileId: '',
      experienceId: '',
      shipId: undefined,
      equipmentId: tempId,
      data: [],
      refetch: () => {},
      isClientMode: true,
      shipTempId: shipTempId,
    });
  };

  const handleClientDelete = (tempId: string) => {
    dispatch(removeClientEquipment(tempId));
    setIsVisible(false);
    setDeleteEquimentId(null);
  };

  const handleDelete = async (preFilledData: ShipCreateEditPayloadI[]) => {
    try {
      setIsDeleting(true);
      const equipment = {
        opr: 'DELETE',
        id: deleteEquipmentId,
      };
      let payload = JSON.parse(JSON.stringify(preFilledData));
      payload[0].designations[0].ships[0]['equipmentCategories'] = [equipment];
      await dispatch(addShipExperience({ payload })).unwrap();
      setShipEquipments((prev) => prev.filter((item) => item.id !== deleteEquipmentId));
      if (refetch) {
        refetch();
      }
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Delete Equipment Category',
            description: 'Failed to Delete',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
      setIsVisible(false);
      setDeleteEquimentId(null);
    }
  };

  return {
    handleAddEditEquipment,
    handleDelete,
    shipEquipments,
    isDeleting,
    isVisible,
    setIsVisible,
    loading,
    setDeleteEquimentId,
    handleClientAdd,
    handleClientEdit,
    handleClientDelete,
    deleteEquipmentId,
  };
};
