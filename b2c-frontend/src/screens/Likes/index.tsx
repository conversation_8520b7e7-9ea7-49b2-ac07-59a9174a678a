import { View } from 'react-native';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useRoute, useNavigation, type RouteProp } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import type { ListItem } from '@/src/components/UsersList/types';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type {
  BottomTabNavigationI,
  HomeStackParamListI,
  RootDrawerParamListI,
} from '@/src/navigation/types';
import LikesList from './components/LikesList';

const LikesScreen = () => {
  const route = useRoute<RouteProp<HomeStackParamListI, 'Likes'>>();
  const bottomTabNavigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const currentUser = useSelector(selectCurrentUser);

  const { postId } = route.params;

  const handleUserPress = (item: ListItem) => {
    if (currentUser.profileId === item.Profile.id) {
      drawerNavigation.navigate('ProfileStack', {
        screen: 'UserProfile',
        params: { profileId: item.Profile.id, fromTabPress: false },
      });
    } else {
      bottomTabNavigation.navigate('HomeStack', {
        screen: 'OtherUserProfile',
        params: { profileId: item.Profile.id, fromTabPress: false },
      });
    }
  };

  return (
    <SafeArea>
      <View className="flex-row items-center px-4">
        <BackButton onBack={() => bottomTabNavigation.goBack()} label="" />
      </View>
      <LikesList postId={postId} onUserPress={handleUserPress} />
    </SafeArea>
  );
};

export default LikesScreen;
