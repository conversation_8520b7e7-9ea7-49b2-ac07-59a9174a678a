import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { AnnouncementState } from './types';

const initialState: AnnouncementState = {
  data: {
    center: [],
    context: [],
    id: '',
    geometry: {
      type: '',
      coordinates: [],
    },
    place_name: '',
    place_type: [],
    properties: {
      accuracy: '',
      mapbox_id: '',
    },
    relevance: 0,
    text: '',
    type: '',
  },
  filters: {
    selfLocation: [],
    otherLocation: [],
    radius: 40,
  },
};

const announcementSlice = createSlice({
  name: 'announcement',
  initialState,
  reducers: {
    setLocationData: (state, action: PayloadAction<any>) => {
      state.data = action.payload;
    },
    clearLocationData: (state) => {
      state.data = {
        center: [],
        context: [],
        id: '',
        geometry: {
          type: '',
          coordinates: [],
        },
        place_name: '',
        place_type: [],
        properties: {
          accuracy: '',
          mapbox_id: '',
        },
        relevance: 0,
        text: '',
        type: '',
      };
    },
    setOtherLocation: (state, action: PayloadAction<any>) => {
      state.filters.otherLocation = action.payload;
    },
    clearOtherLocation: (state) => {
      state.filters.otherLocation = [];
    },
    setSelfLocation: (state, action: PayloadAction<any>) => {
      state.filters.selfLocation = action.payload;
    },
    clearSelfLocation: (state) => {
      state.filters.selfLocation = [];
    },
    setNearbyRadius: (state, action: PayloadAction<any>) => {
      state.filters.radius = action.payload;
    },
  },
});

export const {
  setLocationData,
  clearLocationData,
  setOtherLocation,
  setSelfLocation,
  clearOtherLocation,
  clearSelfLocation,
  setNearbyRadius,
} = announcementSlice.actions;

export default announcementSlice.reducer;
