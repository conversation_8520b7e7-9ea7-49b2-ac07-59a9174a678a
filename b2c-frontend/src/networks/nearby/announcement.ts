import { apiCall } from '@/src/services/api';
import {
  addAnnouncementBodyI,
  attendEventBodyI,
  fetchAnnoucementsBodyI,
  fetchAnnoucementsResultI,
  fetchPeopleAttendingQueryI,
  fetchPeopleAttendingResultI,
} from './types';

export const addAnnouncementAPI = async (payload: addAnnouncementBodyI) => {
  const result = await apiCall('/backend/api/v1/announcement', 'POST', {
    payload,
    isAuth: true,
  });
};

export const fetchAnnoucementsAPI = async (payload: fetchAnnoucementsBodyI) => {
  const result = await apiCall<fetchAnnoucementsBodyI, fetchAnnoucementsResultI>(
    '/backend/api/v1/announcements/fetch',
    'POST',
    {
      payload,
      isAuth: true,
    },
  );

  return result;
};

export const attendUnattendEventAPI = async (payload: attendEventBodyI) => {
  await apiCall('/backend/api/v1/announcement/rsvp', 'POST', {
    payload,
    isAuth: true,
  });
};

export const deleteAnnouncementAPI = async (id: string) => {
  await apiCall('/backend/api/v1/announcements', 'DELETE', {
    query: { id },
    isAuth: true,
  });
};

export const fetchPeopleAttendingAPI = async (query: fetchPeopleAttendingQueryI) => {
  const result = await apiCall<fetchPeopleAttendingQueryI, fetchPeopleAttendingResultI>(
    '/backend/api/v1/announcement/rsvp/profiles',
    'GET',
    {
      query,
      isAuth: true,
    },
  );
  return result;
};
