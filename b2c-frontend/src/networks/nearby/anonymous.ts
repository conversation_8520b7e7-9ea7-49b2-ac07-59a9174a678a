import { apiCall } from '@/src/services/api';

export const toggleAnonymous = async () => {
  const result = await apiCall('/backend/api/v1/auth/session/anonymous', 'PATCH', {
    isAuth: true,
  });
  return result;
};

export const fetchAnonymousStatus = async () => {
  const result = await apiCall<void, boolean>('/backend/api/v1/auth/session/anonymous', 'GET', {
    isAuth: true,
  });
  console.log('result = ', result);
  return result;
};
