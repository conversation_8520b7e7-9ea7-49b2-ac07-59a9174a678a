import React, { useState, useRef } from 'react';
import { View, Text, Pressable, Dimensions } from 'react-native';
import Video from 'react-native-video';
import PlayIcon from '@/src/assets/svgs/Play';
import PauseIcon from '@/src/assets/svgs/Pause';
import type { VideoPlayerProps } from './types';

const SCREEN_WIDTH = Dimensions.get('window').width;
const CONTENT_WIDTH = SCREEN_WIDTH - 32;

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  source,
  width = CONTENT_WIDTH,
  height = CONTENT_WIDTH,
  showControls = true,
  autoPlay = false,
  loop = false,
  muted = false,
  resizeMode = 'cover',
  onPress,
  style,
  borderRadius = 12,
}) => {
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [showControlsOverlay, setShowControlsOverlay] = useState(true);
  const videoRef = useRef<any>(null);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    setShowControlsOverlay(true);
    
    // Hide controls after 3 seconds when playing
    if (!isPlaying) {
      setTimeout(() => {
        setShowControlsOverlay(false);
      }, 3000);
    }
  };

  const handleVideoPress = () => {
    if (onPress) {
      onPress();
    } else {
      handlePlayPause();
    }
  };

  const handleLoad = (data: any) => {
    setDuration(data.duration);
  };

  const handleProgress = (data: any) => {
    setCurrentTime(data.currentTime);
  };

  const handleEnd = () => {
    setIsPlaying(false);
    setCurrentTime(0);
    setShowControlsOverlay(true);
  };

  return (
    <View style={[{ width, height }, style]}>
      <Pressable
        onPress={handleVideoPress}
        style={{
          width: '100%',
          height: '100%',
          borderRadius,
          overflow: 'hidden',
        }}
      >
        <Video
          ref={videoRef}
          source={{ uri: source }}
          style={{
            width: '100%',
            height: '100%',
          }}
          paused={!isPlaying}
          repeat={loop}
          muted={muted}
          resizeMode={resizeMode}
          onLoad={handleLoad}
          onProgress={handleProgress}
          onEnd={handleEnd}
          progressUpdateInterval={1000}
        />
        
        {showControls && showControlsOverlay && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
            }}
          >
            {/* Play/Pause Button */}
            <Pressable
              onPress={handlePlayPause}
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderRadius: 30,
                padding: 15,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              {isPlaying ? (
                <PauseIcon width={24} height={24} fill="#000" />
              ) : (
                <PlayIcon width={24} height={24} fill="#000" />
              )}
            </Pressable>

            {/* Duration Display */}
            <View
              style={{
                position: 'absolute',
                bottom: 10,
                right: 10,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 4,
              }}
            >
              <Text style={{ color: 'white', fontSize: 12, fontWeight: '500' }}>
                {formatTime(currentTime)} / {formatTime(duration)}
              </Text>
            </View>

            {/* Progress Bar */}
            <View
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                height: 3,
                backgroundColor: 'rgba(255, 255, 255, 0.3)',
              }}
            >
              <View
                style={{
                  height: '100%',
                  backgroundColor: '#10B981',
                  width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%',
                }}
              />
            </View>
          </View>
        )}
      </Pressable>
    </View>
  );
};

export default VideoPlayer;
