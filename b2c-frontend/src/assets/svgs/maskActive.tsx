import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Circle, Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const MaskActive: React.FC<OutlinedIconPropsI> = ({
  width = 2.48,
  height = 2.48,
  color = '#404040',
  stroke,
  strokeWidth = 1.5,
  accessibilityLabel = 'MaskActive',
  disabled,
  ...props
}) => {
  const strokeColor = stroke || color;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      viewBox="0 0 450 450"
      fill="none"
      {...props}
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Circle cx={225} cy={225} r={222.5} fill="#FFFDFD" stroke="#000" strokeWidth={5} />
      <Path
        d="M178.231 112.855c-8.17-7.69-12.5-14.37-23.5-19.25-15.51-6.89-27.99-5.28-43.56-.21-21.92 7.14-43.25 16.27-65.25 23.25-5.56 2.05-9.81 6.08-11.76 11.74-1.95 5.66-.92 16.21-.63 22.41 4.16 89.18 31.27 201.08 101.78 261.22 65.18 55.58 153.98 42.43 208.49-20.48 55.26-63.77 71.44-167.28 72.75-249.25.15-9.53 1.26-17.34-8.05-23.46-23.58-8.56-47.15-18.22-71.07-25.93-14.84-4.78-27.45-5.83-42.06.71-14.91 6.67-22.17 20.41-35.86 28.64-26.38 15.85-59.16 11.44-81.29-9.39h.01z"
        fill="#A1A1A1"
        stroke="#010101"
        strokeWidth={2}
        strokeMiterlimit={10}
      />
      <Path
        d="M133.053 166.476c15.64-.24 35.71 3.54 48.87 12.34 16.49 11.03 15.11 25.95-2.77 34.26-21.97 10.21-58.33 7.65-78.95-5.03-15.48-9.52-18.75-23.98-1.38-33.8 9.64-5.45 23.24-7.59 34.23-7.76v-.01z"
        fill="#FFFEFE"
        stroke="#010101"
        strokeWidth={2}
        strokeMiterlimit={10}
      />
      <Path
        d="M314.942 166.445c14.53-.27 53.15 4.79 49.38 26.38-3.67 21-47.68 27.41-64.55 26.49-12.77-.7-42.73-7.05-41.74-24.25 1.18-20.56 41.04-28.32 56.9-28.62h.01z"
        fill="#fff"
        stroke="#010101"
        strokeWidth={2}
        strokeMiterlimit={10}
      />
    </Svg>
  );
};

export default MaskActive;
