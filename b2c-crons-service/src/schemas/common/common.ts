import { DBDataTypeE } from '@consts/common/data';
import {
  CountryISO2R,
  ObjectIdR,
  PersonNameR,
  SemverR,
  TextAlphaNumericSpecialCharR,
  YearR,
} from '@consts/common/regex/regex';

import { z } from 'zod';
export const VersionNoSchema = z.string().regex(SemverR, 'x-version-no is invalid');

export const EmailSchema = z.string().email();
export const UUIDSchema = z.string().uuid();

export type UUIDI = z.infer<typeof UUIDSchema>;
export const UsernameSchema = z
  .string()
  .min(4, 'Username must have at least 4 characters')
  .max(25, 'Username cannot exceed 25 characters')
  .regex(
    /^(?![_.])[a-zA-Z0-9._]+(?<![_.])$/,
    'Username can only contain letters, numbers, periods, and underscores, and cannot start or end with a period/underscore',
  )
  .regex(/^(?!.*[_.]{2})/, 'Username cannot contain consecutive periods/underscores')
  .regex(/^(?!^[._]+$)/, 'Username cannot contain only periods or underscores');

export const PersonFullNameSchema = z
  .string({ required_error: 'Name is required' })
  .min(2, 'Name must be at least 2 characters')
  .max(100, 'Name cannot exceed 100 characters')
  .transform((value) => value.trim().replace(/[\u2018\u2019\u2032]/g, "'"))
  .refine((value) => PersonNameR.test(value), {
    message: 'Full name can only contain letters, spaces, periods and apostrophes',
  })
  .refine((value) => !/\.\./.test(value), {
    message: 'Full name cannot contain consecutive periods',
  });
export const PaginationSchema = z.object({
  page: z.coerce
    .number()
    .int()
    .min(0)
    .default(0)
    .refine((data) => data > -1, { message: 'page must be greater than -1' }),
  pageSize: z.coerce
    .number()
    .int()
    .default(10)
    .refine((data) => data > 0 && data <= 20, {
      message: 'pageSize must be between 0 and 20',
    }),
});
export type PaginationI = z.infer<typeof PaginationSchema>;

export const CursorIdSchema = z
  .union([z.number(), z.literal('null').transform(() => null), z.string(), z.null()])
  .transform((data) => {
    if (data === null || data === undefined || data === 'null') return null;
    const num = Number(data);
    return isNaN(num) ? null : num;
  })
  .refine((data) => data === null || (typeof data === 'number' && data > 0), {
    message: 'cursorId must be null or a positive number',
  });

export const CursorPaginationSchema = z.object({
  cursorId: CursorIdSchema,
  pageSize: z
    .union([z.string(), z.number()])
    .default(10)
    .transform((data) => parseInt(String(data)))
    .refine((data) => data > 0 && data <= 10, {
      message: 'pageSize must be between 1 and 10',
    }),
});
export type CursorPaginationI = z.infer<typeof CursorPaginationSchema>;

export const CursorDatePaginationSchema = z.object({
  cursorDate: z
    .union([z.coerce.date(), z.literal('null').transform(() => null), z.null()])
    .optional()
    .nullable(),
  pageSize: z
    .union([z.string(), z.number()])
    .default(10)
    .transform((data) => parseInt(String(data)))
    .refine((data) => data > 0 && data <= 10, {
      message: 'pageSize must be between 1 and 10',
    }),
});
export type CursorDatePaginationI = z.infer<typeof CursorDatePaginationSchema>;
export const ProfileIdQuerySchema = z.object({
  profileId: UUIDSchema,
});

export const ProfileIdPaginationSchema = PaginationSchema.merge(ProfileIdQuerySchema);

export type ProfileIdPaginationI = z.infer<typeof ProfileIdPaginationSchema>;

export const ProfileIdCursorPaginationSchema = CursorPaginationSchema.merge(ProfileIdQuerySchema);

export type ProfileIdCursorPaginationI = z.infer<typeof ProfileIdCursorPaginationSchema>;

export const RouteParamsSchema = z.object({
  id: UUIDSchema,
});
export type RouteParamsI = z.infer<typeof RouteParamsSchema>;

export const ProfileIdRouteParamsSchema = z.object({
  profileId: UUIDSchema,
});
export type ProfileIdRouteParamsI = z.infer<typeof ProfileIdRouteParamsSchema>;

export const ProfileIdBodySchema = z.object({
  profileId: UUIDSchema,
});
export type ProfileIdBodyI = z.infer<typeof ProfileIdBodySchema>;
export const IdTypeSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});

export type IdTypeI = z.infer<typeof IdTypeSchema>;
export type IdTypeMapI = {
  [key: string]: IdTypeI;
};
export const CountryIso2Schema = z.string().length(2).regex(CountryISO2R);
export const YearSchema = z.string().regex(YearR, 'Invalid year format (must be 4 digits)');
export const NameSchema = z.string().min(1).max(100).regex(TextAlphaNumericSpecialCharR);

export const TextAlphaNumericSpecialCharSchema = z.string().min(1).regex(TextAlphaNumericSpecialCharR);

export const ObjectIdSchema = z.string().regex(ObjectIdR, {
  message: 'Invalid ObjectId format',
});

export const FileOperationSchema = z.object({
  opr: z.enum(['CREATE', 'UPDATE', 'DELETE']),
  fileUrl: z.string().url().optional(),
});
