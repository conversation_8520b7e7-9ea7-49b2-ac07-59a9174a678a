import { HttpStatus } from '@consts/common/api/status';
import { CoreLeaderboardModule } from '@modules/leaderboard/leaderboard';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const healthCheckRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/crons/api/v1/health', {}, async (_request: FastifyRequest, reply: FastifyReply) => {
    await CoreLeaderboardModule.refreshAllLeaderboards();
    reply.status(HttpStatus.OK).send({ status: 'API is UP' });
  });
};

export default healthCheckRoutes;
