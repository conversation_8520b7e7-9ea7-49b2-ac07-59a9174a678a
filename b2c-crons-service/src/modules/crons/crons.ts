import ForumModule from '@modules/forum';
import { QuestionModule } from '@modules/forum/question';
import { LeaderboardModule } from '@modules/leaderboard';
import cron from 'node-cron';

export const CoreCronsModule = {
  run: () => {
    console.log('Cron Service Started');
    cron.schedule('0 */2 * * *', async () => {
      try {
        console.log('Cron: Updating expired live questions...');
        await QuestionModule.updateExpiredLiveQuestions();
      } catch (_error) {
        //
      }
    });
    cron.schedule('0 0 * * 5', async () => {
      try {
        console.log('Cron: Updating Leaderboards...');
        await LeaderboardModule.CoreLeaderboardModule.refreshAllLeaderboards();
      } catch (_error) {
        //
      }
    });
    cron.schedule('0 18 * * *', async () => {
      try {
        console.log('Cron: Notifying about questions...');
        await ForumModule.QuestionModule.notificationsForQuestions();
      } catch (_error) {
        //
      }
    });
  },
};
