import { NotificationCreateOneSchema, type NotificationCreateOneI } from '@schemas/communication/notification';
import AppError from '@classes/AppError';
import CoreCommunicationModule from './communication';
import { toAny } from '@utils/data/object';

const NotificationModule = {
  createOne: async (params: NotificationCreateOneI) => {
    try {
      const { error: notificationCreateOneError } = NotificationCreateOneSchema.safeParse(params);

      if (notificationCreateOneError) {
        throw new AppError('NF001');
      }
      const notificationCreateOneData = toAny(params);
      await CoreCommunicationModule.createOne(notificationCreateOneData?.topic, {
        category: 'NOTIFICATION',
        profileId: notificationCreateOneData?.profileId,
        profileIds: notificationCreateOneData?.profileIds,
        firebaseTopic: notificationCreateOneData?.firebaseTopic,
        notification: {
          actorProfileId: notificationCreateOneData?.actorProfileId,
          actorProfileName: notificationCreateOneData?.actorProfileName,
          commentId: notificationCreateOneData?.commentId,
          parentCommentId: notificationCreateOneData?.parentCommentId,
          postId: notificationCreateOneData?.postId,
          postText: notificationCreateOneData?.postText,
          profileId: notificationCreateOneData?.profileId,
          screen: notificationCreateOneData?.screen,
          type: notificationCreateOneData?.type,
          requestId: notificationCreateOneData?.requestId,
          voteType: notificationCreateOneData?.voteType,
          questionId: notificationCreateOneData?.questionId,
          questionsCount: notificationCreateOneData?.questionCount,
          questionTitle: notificationCreateOneData?.questionTitle,
          answerId: notificationCreateOneData?.answerId,
          answerText: notificationCreateOneData?.answerText,
        },
      });
    } catch (_error) {
      //
    }
  },
};
export default NotificationModule;
