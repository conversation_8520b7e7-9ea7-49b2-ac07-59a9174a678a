import { prismaPG } from '../../config/db';
export const CoreLeaderboardModule = {
  refreshContributionWeeklyLeaderboard: async () => {
    try {
      await prismaPG.$executeRaw`REFRESH MATERIALIZED VIEW "leaderboard"."ContributionWeeklyLeaderboard"`;
    } catch (_) {
      // Optional: handle error silently or send to monitoring
    }
  },

  refreshQnAWeeklyLeaderboard: async () => {
    try {
      await prismaPG.$executeRaw`REFRESH MATERIALIZED VIEW "leaderboard"."QnAAnswerWeeklyLeaderboard"`;
    } catch (_) {
      // handle error silently
    }
  },

  refreshTroubleshootWeeklyLeaderboard: async () => {
    try {
      await prismaPG.$executeRaw`REFRESH MATERIALIZED VIEW "leaderboard"."TroubleshootWeeklyLeaderboard"`;
    } catch (_) {
      // handle error silently
    }
  },

  refreshTotalWeeklyLeaderboard: async () => {
    try {
      await prismaPG.$executeRaw`REFRESH MATERIALIZED VIEW "leaderboard"."TotalWeeklyLeaderboard"`;
    } catch (_) {
      // handle error silently
    }
  },

  refreshContributionOverallLeaderboard: async () => {
    try {
      await prismaPG.$executeRaw`REFRESH MATERIALIZED VIEW "leaderboard"."ContributionOverallLeaderboard"`;
    } catch (_) {
      // handle error silently
    }
  },

  refreshQnAOverallLeaderboard: async () => {
    try {
      await prismaPG.$executeRaw`REFRESH MATERIALIZED VIEW "leaderboard"."QnAAnswerOverallLeaderboard"`;
    } catch (_) {
      // handle error silently
    }
  },

  refreshTroubleshootOverallLeaderboard: async () => {
    try {
      await prismaPG.$executeRaw`REFRESH MATERIALIZED VIEW "leaderboard"."TroubleshootOverallLeaderboard"`;
    } catch (_) {
      // handle error silently
    }
  },

  refreshTotalOverallLeaderboard: async () => {
    try {
      await prismaPG.$executeRaw`REFRESH MATERIALIZED VIEW "leaderboard"."TotalOverallLeaderboard"`;
    } catch (_) {
      // handle error silently
    }
  },

  refreshAllLeaderboards: async () => {
    try {
      await Promise.all([
        CoreLeaderboardModule.refreshContributionWeeklyLeaderboard(),
        CoreLeaderboardModule.refreshQnAWeeklyLeaderboard(),
        CoreLeaderboardModule.refreshTroubleshootWeeklyLeaderboard(),
        CoreLeaderboardModule.refreshTotalWeeklyLeaderboard(),
        CoreLeaderboardModule.refreshContributionOverallLeaderboard(),
        CoreLeaderboardModule.refreshQnAOverallLeaderboard(),
        CoreLeaderboardModule.refreshTroubleshootOverallLeaderboard(),
        CoreLeaderboardModule.refreshTotalOverallLeaderboard(),
      ]);
    } catch (_) {
      // handle batch-level error silently
    } finally {
      await prismaPG.$disconnect();
    }
  },
};
