import { ModuleE, SubModuleE } from '@prisma/mongodb';
import { prismaPG } from '../../config/db';
import AppConfig from '@modules/appConfig';
import { ForumQuestionConfigI } from '@interfaces/appConfig/appConfig';
import { getCurrentDate, subHrToDate, subMsToDate } from '@utils/data/date';
import CommunicationModule from '@modules/communication';

export const QuestionModule = {
  updateExpiredLiveQuestions: async (): Promise<number> => {
    const config = (await AppConfig.AppConfigModule.fetchById(
      { module: ModuleE.FORUM, subModule: SubModuleE.QUESTION },
      { config: true },
    )) as ForumQuestionConfigI;
    const ms24HoursAgo = subMsToDate({ date: getCurrentDate(), ms: config.liveExpiry });

    const questionsToUpdate = await prismaPG.question.findMany({
      where: {
        isLive: true,
        liveStartedAt: {
          lt: ms24HoursAgo,
        },
      },
      select: {
        id: true,
      },
    });
    if (!questionsToUpdate.length) {
      return;
    }

    const _updatedResult = await prismaPG.question.updateMany({
      where: {
        id: {
          in: questionsToUpdate.map((q) => q.id),
        },
      },
      data: {
        isLive: false,
      },
    });
    return _updatedResult.count;
  },
  notificationsForQuestions: async (): Promise<void> => {
    const currentDate = getCurrentDate();
    const hours24Ago = subHrToDate({ date: currentDate, hours: 24 });

    const questionsCount = await prismaPG.question.count({
      where: {
        createdAt: {
          gte: hours24Ago,
        },
      },
    });

    if (questionsCount > 0) {
      const profileIdsArray = await prismaPG.profile.findMany({
        select: { id: true },
      });

      const profileIds = profileIdsArray.map((item) => item.id);

      await CommunicationModule.NotificationModule.createOne({
        firebaseTopic: 'public',
        questionsCount,
        topic: 'communication_topic',
        type: 'FORUM_QUESTIONS',
        profileIds,
      });
    }

    return;
  },
};
